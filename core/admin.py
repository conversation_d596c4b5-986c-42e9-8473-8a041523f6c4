from import_export.admin import ImportExportModelAdmin

from django.contrib import admin
from django.contrib.admin.models import LogEntry

from . import models, resources


# Register your model(s) here.
@admin.register(LogEntry)
class LogEntryAdmin(ImportExportModelAdmin):
    date_hierarchy = 'action_time'
    list_filter = [
        'user',
        'content_type',
        'action_flag'
    ]
    search_fields = [
        'object_repr',
        'change_message'
    ]
    list_display = [
        'action_time',
        'user',
        'content_type',
        'action_flag',
    ]

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser


class OTPResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.OTPResource
    search_fields = [
        "recipient"
    ]
    list_filter = ()
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WhitelistedIPAddressResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.WhitelistedIPAddressResource
    search_fields = [
        "ip",
        "company__email"
    ]
    list_filter = (
        "is_active",
        "company"
    )
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OneTimeAccountRequestAdmin(ImportExportModelAdmin):
    resource_class = resources.OneTimeAccountRequestResource
    search_fields = [
        "nuban",
        "request_reference",
    ]
    list_filter = [
        "created_at",
        "request_active",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OfflineAccountRecordAdmin(ImportExportModelAdmin):
    resource_class = resources.OfflineAccountRecordResource
    search_fields = [
        "nuban",
        "unique_code",
        "confirmation_code",
    ]
    list_filter = [
        "created_at",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(models.OTP, OTPResourceAdmin)
admin.site.register(
    models.WhitelistedIPAddress, WhitelistedIPAddressResourceAdmin
)
admin.site.register(models.OneTimeAccountRequest, OneTimeAccountRequestAdmin)
admin.site.register(models.OfflineAccountRecord, OfflineAccountRecordAdmin)
