from django.db import models
from django.utils.translation import gettext as _
from django.core.cache import cache
from django.db.models import QuerySet

from vfd_bank.apis import VfdBankMgr


class FinancialInstitution(models.Model):
    code = models.CharField(max_length=20, unique=True, editable=False)
    name = models.CharField(max_length=255, editable=False)
    logo = models.TextField(blank=True, null=True)
    bank_code = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    def __str__(self):
        return self.name

    @classmethod
    def update_bank_list(cls):
        """
        Fetches the latest list of banks from VfdBankMgr and updates the database.

        This method retrieves the bank list from an external service, checks the response status,
        and updates or creates bank records in the database accordingly.

        Raises:
            Exception: If the API call fails or returns an error status.
        """

        # Fetch the bank list from the external API
        bank_list = VfdBankMgr.get_bank_list()

        # Check if the API response indicates success
        if bank_list.get("status") == "success":
            # Extract the list of banks from the response
            banks = bank_list["response"]["data"]["bank"]

            # Prepare a list for bulk creation
            bank_objects = []
            existing_codes = set(cls.objects.values_list("code", flat=True))

            # Iterate through each bank entry and prepare records for bulk creation
            for bank_data in banks:
                if bank_data["code"] not in existing_codes:
                    bank_objects.append(
                        cls(
                            code=bank_data[
                                "code"
                            ],  # Use 'code' as the unique identifier
                            name=bank_data["name"],  # Set bank name
                            logo=bank_data.get(
                                "logo", ""
                            ),  # Store logo (may be Base64, default empty string)
                            bank_code=bank_data.get(
                                "bank_code", ""
                            ),  # Store bank code (default empty string)
                        )
                    )

            # Bulk create the bank records if there are any new entries
            if bank_objects:
                # Try to retrieve the cached bank list
                # bank_list_cache = cache.delete("ngn_bank_list_cache")
                cls.objects.bulk_create(bank_objects)

            # Return all bank records after the update
            return cls.objects.all()

        else:
            # Raise an exception if the API request fails, providing the error message if available
            raise Exception(
                "Failed to fetch bank list: "
                + bank_list.get("message", "Unknown error")
            )

    @classmethod
    def fetch_and_cache_ngn_bank_list(cls) -> QuerySet:
        """
        This function retrieves the latest bank list, caches it for 6 hours,
        and returns the bank list. If the cached data is not available,
        it calls the update_bank_list method to fetch and create the data.
        """
        # Try to retrieve the cached bank list
        bank_list_cache = cache.get("ngn_bank_list_cache")
        if not bank_list_cache:
            bank_list = cls.update_bank_list()

            cache.set(key="ngn_bank_list_cache", value=bank_list, timeout=60 * 60 * 6)
            return bank_list
        else:
            return bank_list_cache
        # If cache is not available, call the update_bank_list method
