from django.contrib import admin
from import_export.admin import ImportExportModelAdmin

from vfd_bank.resources import *


class FinancialInstitutionAdmin(ImportExportModelAdmin):
    resource_class = FinancialInstitutionResource
    search_fields = [
        "name",
        "code",
        "bank_code",
    ]
    list_filter = []
    # date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(FinancialInstitution, FinancialInstitutionAdmin)
