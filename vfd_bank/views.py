from django.shortcuts import render

from helpers.custom_responses import Response
from rest_framework.views import APIView

from rest_framework import status
from rest_framework.pagination import PageNumberPagination

from vfd_bank.models import FinancialInstitution


from helpers.reusable import Paginator  # Ensure Paginator is imported


from vfd_bank.serializers import FinancialInstitutionSerializer


class CustomPageNumberPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = "page_size"


class FetchBanksView(APIView):
    # serializer_class = FinancialInstitutionSerializer

    def get(self, request):
        name = request.GET.get("name")

        if not name:
            bank_query_set = (
                FinancialInstitution.fetch_and_cache_ngn_bank_list()
                .order_by("name")
                .values("code", "name", "logo", "bank_code")
            )
        else:
            bank_query_set = (
                FinancialInstitution.fetch_and_cache_ngn_bank_list()
                .filter(name__icontains=name)
                .order_by("name")
                .values("code", "name", "logo", "bank_code")
            )

        # paginated_data = Paginator.paginate(
        #         request=request, queryset=bank_query_set
        #     )
        # serializer = self.serializer_class(
        #     instance=paginated_data, many=True)
        # data = {
        #     "message": "successfully fetched company account details.",
        #     "account_details": serializer.data,
        # }
        # print(data)

        paginator = PageNumberPagination()
        paginator.page_size = 30
        paginated_queryset = paginator.paginate_queryset(bank_query_set, request)

        return paginator.get_paginated_response(
            {"message": "Successfully fetched banks", "data": paginated_queryset}
        )
