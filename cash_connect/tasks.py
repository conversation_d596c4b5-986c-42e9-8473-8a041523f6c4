from celery import shared_task

from accounts.models import TransactionDetail, TransferMoneyRequest
from cash_connect.helpers.cash_connect_base_file import CASH_CONNECT_STATUS_CODES
from cash_connect.helpers.manage_request import CashConnectRequestManagers
from cash_connect.models import CashConnectVirtualAccount, CashConnectWebHookData
from fidelity.models import AccountSystem
from helpers import enums
from decouple import config
from datetime import datetime
import pytz
from django.conf import settings
import json

from helpers.reusable import make_request

@shared_task
def verify_cash_connect_bank_transfer():
    pending_transaction_reference = TransactionDetail.objects.filter(
        transaction_status = enums.TransactionStatus.PENDING, service_provider = enums.ServiceProvider.CASH_CONNECT)


    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Hook {settings.CALLBACK_BEARER}",
    }

    SUCCESS_CODES = ["00"]

    FAILURE_CODES = [code for code in CASH_CONNECT_STATUS_CODES.keys() if code != "00"]

    for transaction in pending_transaction_reference:
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        verification_response = CashConnectRequestManagers().handle_disbursement_verification(transaction_reference = f'{config("CASH_CONNECT_PARTNER_CODE")}-{transaction.company_reference}')
        if not isinstance(verification_response, dict):
            continue


        processor_status_code = verification_response.get("processorStatusCode")

        if processor_status_code in SUCCESS_CODES:

            session_id = verification_response.get("data", {}).get("sessionId")
            source_account = verification_response.get("data", {}).get("sourceAccountNumber")
            bank_name = verification_response.get("data", {}).get("beneficiaryBankName")
            account_number = verification_response.get("data", {}).get("beneficiaryAccountNumber")
            transaction_amount = verification_response.get("data", {}).get("transactionAmount")
            transaction_value_amount = verification_response.get("data", {}).get("transactionValueAmount")
            fee = verification_response.get("data", {}).get("transactionFeeAmount")
            narration = verification_response.get("data", {}).get("narration")


            transaction.transaction_status = enums.TransactionStatus.SUCCESSFUL
            transaction.session_id = session_id
            transaction.verification_response = verification_response
            transaction.source_account = source_account
            transaction.bank_name = bank_name
            transaction.save()

            # get transfer request instance
            try:
                transfer_db_instance = TransferMoneyRequest.objects.get(request_reference = transaction.company_reference)
            except TransferMoneyRequest.DoesNotExist:
                continue
            

            transfer_db_instance.session_id = session_id
            transfer_db_instance.verified_at = TODAY
            transfer_db_instance.verification_response = verification_response
            transfer_db_instance.status = enums.TransactionStatus.SUCCESSFUL
            transfer_db_instance.save()


            # send webhook data to product
            wehbook_payload = json.dumps({
                "type_of_webhook": "DISBURSEMENT_WEBHOOK",
                "transaction_reference": transaction.company_reference,
                "session_id": session_id,
                "account_number": account_number,
                "bank_name": bank_name,
                "transaction_amount": transaction_amount,
                "transaction_value_amount": transaction_value_amount,
                "transaction_fee": fee,
                "narration": narration,
                "status": enums.TransactionStatus.SUCCESSFUL,
                "reversed": False,
            })

            company_url = transaction.company.cash_connect_callback_url
            if company_url != None:

                response = make_request(
                    "POST", dict(url=company_url, headers=headers, data=wehbook_payload)
                )
                if response.get("status_code") == 200:
                    transaction.event_sent = True
                    transaction.company_event_response = json.dumps(
                        response.get("data"))
                else:
                    transaction.company_event_response = json.dumps(
                        response.get("data"))

                transaction.event_send_count += 1
                transaction.event_updated_at = TODAY
                transaction.save()
            

            continue


        elif processor_status_code in FAILURE_CODES:
            pass
            # # Perform cashconnect Reversal
            # AccountSystem.perform_transfer_reversal(
            #     query=transaction,
            #     service_provider=enums.ServiceProvider.CASH_CONNECT,
            # )

            

@shared_task
def send_company_transaction_callbacks_cash_connect(db_id):
    get_transaction = CashConnectWebHookData.objects.filter(
        id=db_id
    ).first()

    if get_transaction is None:
        return f"CREDIT TRANSACTION NOT FOUND FOR: {db_id}."
    
    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))


    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Hook {settings.CALLBACK_BEARER}",
    }

    # get virtual account model instance
    try:
        virtual_account_instance = CashConnectVirtualAccount.objects.get(account_number = get_transaction.beneficiary_account_number)
        company_instance = virtual_account_instance.company
    except CashConnectVirtualAccount.DoesNotExist:
        return f"VIRTUAL ACCOUNT NOT FOUND FOR: {get_transaction.beneficiary_account_number}"

    wehbook_payload = json.dumps({
        "one_time": True,
        "request_reference": get_transaction.transaction_reference,
        "company": company_instance.name,
        "recipient_account_name": get_transaction.beneficiary_account_name,
        "recipient_account_number": get_transaction.beneficiary_account_number,
        "amount": float(get_transaction.transaction_amount),
        "fee": float(get_transaction.transaction_fee),
        "amount_payable": float(get_transaction.transaction_value_amount),
        "reference": str(get_transaction.transaction_reference),
        "transaction_type": enums.TransactionType.CREDIT,
        "payer_account_name": get_transaction.source_account_name,
        "payer_account_number": get_transaction.source_account_number,
        "payer_bank_code": get_transaction.source_bank_code,
        "paid_at": str(get_transaction.created_at),
        "narration": get_transaction.narration,
        "session_id": get_transaction.session_id,
        "transaction_reference": get_transaction.transaction_reference,
        "settlement_status": True,
        "currency": get_transaction.currency,
        "provider": "CASH CONNECT",
    })

    company_url = company_instance.cash_connect_callback_url
    if company_url != None:

        response = make_request(
            "POST", dict(url=company_url, headers=headers, data=wehbook_payload)
        )
        if response.get("status_code") == 200:
            get_transaction.event_sent = True
            get_transaction.company_event_response = json.dumps(
                response.get("data"))
        else:
            get_transaction.company_event_response = json.dumps(
                response.get("data"))
            
        
        get_transaction.event_send_count += 1
        get_transaction.event_updated_at = TODAY
        get_transaction.save()
        return response
    else:
        get_transaction.event_send_count += 1
        get_transaction.event_updated_at = TODAY
        get_transaction.save()
        return response


            



@shared_task
def resend_company_transaction_callbacks_cash_connect():

    pending_events = CashConnectWebHookData.objects.filter(
        event_sent=False,
        event_send_count__lte=4,
    )
    if pending_events.exists():
        for event in pending_events:
            send_company_transaction_callbacks_cash_connect.delay(event.id)
        return "EVENTS RETRIED SUCCESSFULLY."
    return "THERE ARE NO PENDING EVENTS."


@shared_task
def process_pending_cash_connect_transactions():
    pending_transaction_reference = TransactionDetail.objects.filter(
        transaction_status = enums.TransactionStatus.PENDING,
        service_provider = enums.ServiceProvider.CASH_CONNECT
        )

    SUCCESS_CODES = ["00"]
    FAILURE_CODES = [code for code in CASH_CONNECT_STATUS_CODES.keys() if code != "00"]

    for transaction in pending_transaction_reference:
        company_instance = transaction.company
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        if company_instance is None:
            return "Company not found"

        verification_response = CashConnectRequestManagers().handle_disbursement_verification(
            transaction_reference=f'{config("CASH_CONNECT_PARTNER_CODE")}-{transaction.company_reference}'
            )
        if not isinstance(verification_response, dict):
            continue

        processor_status_code = verification_response.get("processorStatusCode")

        if processor_status_code in SUCCESS_CODES:
            session_id = verification_response.get("data", {}).get("sessionId")
            source_account = verification_response.get("data", {}).get("sourceAccountNumber")
            bank_name = verification_response.get("data", {}).get("beneficiaryBankName")
            account_number = verification_response.get("data", {}).get("beneficiaryAccountNumber")
            transaction_amount = verification_response.get("data", {}).get("transactionAmount")
            transaction_value_amount = verification_response.get("data", {}).get("transactionValueAmount")
            fee = verification_response.get("data", {}).get("transactionFeeAmount")
            narration = verification_response.get("data", {}).get("narration")

            transaction.transaction_status = enums.TransactionStatus.SUCCESSFUL
            transaction.session_id = session_id
            transaction.verification_response = verification_response
            transaction.source_account = source_account
            transaction.bank_name = bank_name
            transaction.save()

            # get transfer request instance
            try:
                transfer_db_instance = TransferMoneyRequest.objects.get(
                    request_reference = transaction.company_reference
                    )
            except TransferMoneyRequest.DoesNotExist:
                continue
            
            transfer_db_instance.session_id = session_id
            transfer_db_instance.verified_at = TODAY
            transfer_db_instance.verification_response = verification_response
            transfer_db_instance.status = enums.TransactionStatus.SUCCESSFUL
            transfer_db_instance.save()

            company_instance = transaction.company

        elif processor_status_code in FAILURE_CODES:

            # Refund Company wallet
            # Perform cashconnect Reversal
            transaction.transaction_status = enums.TransactionStatus.FAILED
            transaction.verification_response = verification_response
            transaction.save()

            AccountSystem.perform_transfer_reversal(
                query=transaction,
                service_provider=enums.ServiceProvider.CASH_CONNECT,
            )
