from rest_framework import serializers
from accounts.models import AccountDetail
from user_profiles.models import SubCompany
from fidelity.models import AccountSystem, TransferRequest

from helpers.enums import ServiceProvider, AccountType


# Create your serializer(s) here.
class AccountCreationSerializer(serializers.Serializer):
    # customer_ref = serializers.Char<PERSON><PERSON>(max_length=2000)
    customer_firstname = serializers.<PERSON><PERSON><PERSON><PERSON>(max_length=2000)
    customer_surname = serializers.<PERSON><PERSON><PERSON><PERSON>(max_length=2000)
    customer_email = serializers.Char<PERSON><PERSON>(max_length=2000)
    customer_mobile_no = serializers.Char<PERSON><PERSON>(max_length=2000)
    name_on_account = serializers.Char<PERSON><PERSON>(max_length=2000)
    customer_middle_name = serializers.Cha<PERSON><PERSON><PERSON>(max_length=2000)
    date_of_birth = serializers.Char<PERSON><PERSON>(max_length=2000)
    gender = serializers.Char<PERSON><PERSON>(max_length=2000)
    title = serializers.<PERSON><PERSON><PERSON><PERSON>(max_length=2000)
    address_line_1 = serializers.Cha<PERSON><PERSON><PERSON>(max_length=2000)
    address_line_2 = serializers.Char<PERSON>ield(max_length=2000)
    city = serializers.Char<PERSON><PERSON>(max_length=2000)
    state = serializers.Char<PERSON><PERSON>(max_length=2000)
    country = serializers.CharField(max_length=2000)
    transaction_ref = serializers.CharField(max_length=2000)
    # transaction_desc = serializers.CharField(max_length=2000)
    customer_bvn = serializers.CharField(max_length=2000)

    def validate(self, attrs):
        request = self.context["request"]
        company = request.user
        customer_bvn = attrs.pop("customer_bvn")
        sub_company = SubCompany.identify_as_user(request=request)

        if not company.is_authenticated:
            company = sub_company.company

        account = AccountSystem.objects.create(
            company=company, sub_company=sub_company,
            customer_bvn=customer_bvn,
        )
        attrs["amount"] = 0
        attrs["account"] = account
        attrs["request_ref"] = account.request_ref
        return attrs


class TransferRequestSerializer(serializers.Serializer):
    amount = serializers.IntegerField()
    customer_ref = serializers.CharField()
    customer_firstname = serializers.CharField()
    customer_surname = serializers.CharField()
    customer_email = serializers.CharField()
    customer_mobile_no = serializers.CharField()
    transaction_ref = serializers.CharField()
    destination_account = serializers.CharField()
    destination_bank_code = serializers.CharField()
    transaction_desc = serializers.CharField()

    def validate(self, attrs):
        request = self.context["request"]
        company = request.user
        sub_company = SubCompany.identify_as_user(request=request)

        if not company.is_authenticated:
            company = sub_company.company

        transfer_instance = TransferRequest.objects.create(
            company=company, sub_company=sub_company, amount=attrs["amount"]
        )

        try:
            company_source_account = AccountDetail.objects.get(
                company=company,
                sub_company=sub_company,
                provider=ServiceProvider.FIDELITY,
                account_type=AccountType.MAIN,
            )
        except AccountDetail.DoesNotExist:
            raise serializers.ValidationError(
                {"error": "Account details not found"})

        if company_source_account.cash_balance <= attrs.get("amount", 0.00):
            raise serializers.ValidationError({"Error": "Insufficient funds."})

        attrs["transfer_instance"] = transfer_instance
        attrs["request_ref"] = transfer_instance.request_ref
        attrs["company_source_account"] = company_source_account.account_number

        return attrs


class AccountBalanceSerializer(serializers.Serializer):
    customer_ref = serializers.CharField()
    customer_firstname = serializers.CharField()
    customer_surname = serializers.CharField()
    customer_email = serializers.CharField()
    customer_mobile_no = serializers.CharField()
    transaction_ref = serializers.CharField()
    # account_number = serializers.CharField()
