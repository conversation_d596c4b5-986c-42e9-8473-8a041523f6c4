from django.utils import timezone
from django.conf import settings
from celery import shared_task
from accounts.models import AccountDetail, TransactionDetail
from fidelity.models import (
    AccountSystem,
    TransferRequest,
    AccountInflowPayload,
)
from fidelity.model_choices import Status, TransactionType
from helpers.enums import ServiceProvider, AccountType, TransactionStatus, APIMode
from helpers.reusable import email_sender

from datetime import timedelta

import ast
import uuid
import requests
import json


@shared_task
def update_account_instance(instance, data):
    try:
        instance = AccountSystem.objects.get(id=instance)
    except AccountSystem.DoesNotExist:
        return

    instance.update_account_instance(data)


@shared_task
def update_transfer_instance(instance, data):
    try:
        instance = TransferRequest.objects.get(id=instance)
    except TransferRequest.DoesNotExist:
        return
    instance.update_transfer_instance(instance, data)


@shared_task
def resolve_pending_transactions():
    """
    Task to routinely verify and resolve the status
    of Fidelity transfers.
    """

    from fidelity.helpers.helpers import FidelityClass

    five_minutes_ago = timezone.now() - timedelta(minutes=5)
    _5_days_ago = timezone.now() - timedelta(days=5)

    pending_transactions_qs = TransferRequest.objects.filter(
        status=Status.PENDING,
        created_at__gte=_5_days_ago,
        created_at__lte=five_minutes_ago,
    )

    for query in pending_transactions_qs:
        query: TransferRequest
        company_instance = query.company

        if query.transaction_ref:
            check_trans = FidelityClass.check_transaction_status(
                transaction_ref=query.transaction_ref
            )

            if check_trans.get("data", {}).get("status").lower() == "successful":
                query.status = Status.SUCCESS
                if query.transaction_detail_instance:
                    query.transaction_detail_instance.transaction_status = (
                        TransactionStatus.SUCCESSFUL
                    )
            elif check_trans.get("data", {}).get("status").lower() == "failed":
                query.status = Status.FAILED
                if query.transaction_detail_instance:
                    query.transaction_detail_instance.transaction_status = (
                        TransactionStatus.FAILED
                    )

                try:
                    # Perform Fidelity Reversal
                    AccountSystem.perform_transfer_reversal(
                        query=query,
                        service_provider=ServiceProvider.FIDELITY,
                    )
                except Exception as e:
                    print(e)
            else:
                pass

            query.verification_response = check_trans
            query.save()
            query.transaction_detail_instance.save()
    return "Process completed"


@shared_task
def process_inflow_transaction():
    """
    Task to resolve inflow transactions.
    - Looks through AccountInflowPayload logs routinely
    for unresolved logs.
    """

    pending_inflows = AccountInflowPayload.objects.filter(is_processed=False)

    for query in pending_inflows:
        query: AccountInflowPayload

        payload = ast.literal_eval(query.payload)

        if settings.ENVIRONMENT == "development":
            api_mode = APIMode.TEST
        else:
            api_mode = APIMode.LIVE

        if payload.get("details", {}).get("status", "").lower() == "successful":
            customer_ref = payload.get("details").get("customer_ref")

            try:
                customer_account = AccountSystem.objects.get(
                    customer_ref=uuid.UUID(customer_ref)
                )
            except AccountSystem.DoesNotExist:
                continue

            company = customer_account.company

            reference = payload.get("request_ref")
            status_meassage = payload.get("details").get("data").get("statusmessage")
            recipient_account_name = (
                payload.get("details").get("data").get("craccountname")
            )
            recipient_account_number = (
                payload.get("details").get("data").get("account_number")
            )
            recipient_bank_code = payload.get("details").get("data").get("bank_code")

            amount = payload.get("details").get("data").get("amount")
            payer_account_name = (
                payload.get("details").get("data").get("originatorname")
            )
            payer_account_number = (
                payload.get("details").get("data").get("originatoraccountnumber")
            )

            narration = payload.get("details").get("data").get("narration")
            session_id = payload.get("details").get("data").get("sessionid")

            # Create Fidelity Transaction instance
            transaction_instance = TransferRequest.objects.create(
                company=company,
                status=Status.SUCCESS
                if status_meassage.lower() == "success"
                else Status.FAILED,
                destination_institution_code=recipient_bank_code,
                beneficiary_account_name=recipient_account_name,
                beneficiary_account_number=recipient_account_number,
                originator_account_name=payer_account_name,
                originator_account_number=payer_account_number,
                narration=narration,
                transaction_final_amount=amount,
                amount=amount,
                reference=reference,
                transaction_type=TransactionType.CREDIT,
                payload=payload,
            )

            # Create General Transaction Detail instance
            transaction_detail = TransactionDetail.objects.create(
                company=company,
                beneficiary_account_number=recipient_account_number,
                beneficiary_account_name=recipient_account_name,
                amount=amount,
                fee=0.0,
                amount_payable=amount,
                transaction_type=TransactionType.CREDIT,
                transaction_status=TransactionStatus.SUCCESSFUL
                if status_meassage.lower() == "success"
                else TransactionStatus.FAILED,
                narration=narration,
                service_provider=ServiceProvider.FIDELITY,
                mode=api_mode,
                bank_code=recipient_bank_code,
                source_account=payer_account_number,
                company_reference=str(company.id),
                session_id=session_id,
            )

            query.is_processed = True
            query.status = (
                Status.SUCCESS
                if status_meassage.lower() == "success"
                else Status.FAILED
            )
            query.transaction_id = transaction_instance.request_ref
            query.save()

            if status_meassage.lower() == "success":
                # FUND CUSTOMER ACCOUNT
                fund_virtual_wallet = AccountSystem.fund_customer_account(
                    account=customer_account,
                    amount=amount,
                )

                if fund_virtual_wallet:
                    query.virtual_wallet_funded = True
                    query.save()

                # Fund Company Account
                fund_main_wallet = AccountDetail.fund_account(
                    company=customer_account.company,
                    account_type=AccountType.MAIN,
                    amount=float(amount),
                    charges=0.00,
                    sub_company=customer_account.sub_company,
                    provider=ServiceProvider.FIDELITY,
                )

                if fund_main_wallet.get("status"):
                    query.main_wallet_funded = True
                    query.save()

                    transaction_detail.balance_before = fund_main_wallet.get(
                        "previous_balance"
                    )
                    transaction_detail.balance_after = fund_main_wallet.get(
                        "account_balance"
                    )
                    transaction_detail.save()
                email_notify_owner_on_inflow(transaction_instance)
    return "Process completed"


@shared_task
def notify_companies_on_inflow():
    """ "
    Task to send inflow notification event to companies that
    own the transactions.
    """
    pending_inflow_notifications = AccountInflowPayload.objects.filter(
        is_processed=True, owner_notified=False
    )

    for query in pending_inflow_notifications:
        query: AccountInflowPayload

        payload = ast.literal_eval(query.payload)

        if payload.get("details", {}).get("status", "").lower() == "successful":
            customer_ref = payload.get("details").get("customer_ref")

            try:
                customer_account = AccountSystem.objects.get(
                    customer_ref=uuid.UUID(customer_ref)
                )
            except AccountSystem.DoesNotExist:
                continue

            company = customer_account.company
            sub_company = customer_account.sub_company
            company_callback_url = company.fidelity_callback_url

            reference = payload.get("request_ref")
            recipient_account_name = (
                payload.get("details").get("data").get("craccountname")
            )
            recipient_account_number = (
                payload.get("details").get("data").get("account_number")
            )
            amount = payload.get("details").get("data").get("amount")
            payer_account_name = (
                payload.get("details").get("data").get("originatorname")
            )
            payer_account_number = (
                payload.get("details").get("data").get("originatoraccountnumber")
            )
            payer_bank_code = (
                payload.get("details").get("data").get("originatorcbncode")
            )
            narration = payload.get("details").get("data").get("narration")
            session_id = payload.get("details").get("data").get("sessionid")
            status = payload.get("details").get("data").get("statusmessage")
            paid_at = payload.get("details").get("data").get("trandate")

            request_data = {
                "one_time": customer_account.one_time,
                "request_reference": customer_account.request_reference,
                "company": company.name,
                "sub_company": (
                    sub_company.company_name if sub_company is not None else None
                ),
                "sub_company_email": (
                    sub_company.company_email if sub_company is not None else None
                ),
                "sub_company_unique_id": (
                    sub_company.unique_id if sub_company is not None else None
                ),
                "recipient_account_name": recipient_account_name,
                "recipient_account_number": recipient_account_number,
                "amount": amount,
                "fee": "0.00",
                "amount_payable": amount,
                "reference": reference,
                "transaction_type": "CREDIT",
                "payer_account_name": payer_account_name,
                "payer_account_number": payer_account_number,
                "payer_bank_code": payer_bank_code,
                "paid_at": paid_at,
                "narration": narration,
                "session_id": session_id,
                "transaction_reference": reference,
                "settlement_status": True if status.lower() == "success" else False,
                "currency": "NGN",
                "provider": "FIDELITY",
            }

            try:
                headers = {"content-type": "application/json"}
                resp = requests.request(
                    "POST",
                    url=company_callback_url,
                    headers=headers,
                    data=json.dumps(request_data),
                )
                response = resp.text

                if resp.status_code == 200:
                    query.owner_notified = True
            except Exception as e:
                response = str(e)

            query.company_notification_feedback = response
            query.save()

            transaction_detail_instance = TransactionDetail.objects.filter(
                session_id=session_id
            ).last()
            try:
                transaction_detail_instance.company_event_response = response
            except Exception as e:
                pass
            transaction_detail_instance.save()
        else:
            continue

    return "Process completed"


@shared_task
def notify_owner_about_inflow(owner_emails, amount, account_number, name, balance_afer):
    email_response = email_sender(
        recipient=owner_emails,
        subject="Notification: Fidelity Bank Account Inflow",
        template_directory="inflow_notification.html",
        use_template=True,
        amount=amount,
        account_number=account_number,
        name=name,
        balance_afer=balance_afer,
    )
    return str(email_response)


@shared_task()
def email_notify_owner_on_inflow(instance: TransferRequest):
    if (
        instance.transaction_type == TransactionType.CREDIT
        and instance.status == Status.SUCCESS
    ):
        if instance.company.email == "<EMAIL>":
            owner_emails = [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ]
        else:
            owner_emails = [instance.company.email]
            return

        amount = instance.amount
        account_number = instance.beneficiary_account_number
        name = instance.beneficiary_account_name
        account_details = AccountDetail.objects.filter(
            company=instance.company,
            sub_company=instance.sub_company,
            account_type=AccountType.MAIN,
            provider=ServiceProvider.FIDELITY,
        ).last()

        balance_afer = account_details.cash_balance

        send_email = notify_owner_about_inflow(
            owner_emails,
            amount,
            account_number,
            name,
            balance_afer=balance_afer,
        )

        return send_email
