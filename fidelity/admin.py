from django.contrib import admin
from django.conf import settings
from import_export.admin import ImportExportModelAdmin
from import_export import resources
from accounts.models import AccountDetail, TransactionDetail
from fidelity.models import (
    AccountSystem,
    TransferRequest,
    AccountInflowPayload,
    CallbackNotificationLog,
    FidelityRequestLog,
)
from fidelity.tasks import email_notify_owner_on_inflow
from fidelity.model_choices import Status, TransactionType
from helpers.enums import AccountType, ServiceProvider, TransactionStatus, APIMode
import requests
import json
import ast
import uuid


class AccountSystemResource(resources.ModelResource):
    class Meta:
        model = AccountSystem


class TransferRequestResource(resources.ModelResource):
    class Meta:
        model = TransferRequest


class AccountInflowPayloadResource(resources.ModelResource):
    class Meta:
        model = AccountInflowPayload


class CallbackNotificationLogResource(resources.ModelResource):
    class Meta:
        model = CallbackNotificationLog


class FidelityRequestLogResource(resources.ModelResource):
    class Meta:
        model = FidelityRequestLog


class AccountSystemResourceAdmin(ImportExportModelAdmin):
    resource_class = AccountSystemResource
    list_filter = [
        "created_at",
        "one_time",
    ]
    search_fields = [
        # "company",
        "account_number",
        "reference",
        "transation_reference",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AccountInflowPayloadResourceAdmin(ImportExportModelAdmin):
    resource_class = AccountInflowPayloadResource
    list_filter = ["created_at"]
    search_fields = [
        "ip_address",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def process_inflow_transaction(self, request, queryset):
        for query in queryset:
            query: AccountInflowPayload

            if query.is_processed is False:
                pass
            else:
                continue

            payload = ast.literal_eval(query.payload)

            if settings.ENVIRONMENT == "development":
                api_mode = APIMode.TEST
            else:
                api_mode = APIMode.LIVE

            if payload.get("details", {}).get("status", "").lower() == "successful":
                customer_ref = payload.get("details").get("customer_ref")

                try:
                    customer_account = AccountSystem.objects.get(
                        customer_ref=uuid.UUID(customer_ref)
                    )
                except AccountSystem.DoesNotExist:
                    continue

                company = customer_account.company

                reference = payload.get("request_ref")
                status_meassage = (
                    payload.get("details").get("data").get("statusmessage")
                )
                recipient_account_name = (
                    payload.get("details").get("data").get("craccountname")
                )
                recipient_account_number = (
                    payload.get("details").get("data").get("account_number")
                )
                recipient_bank_code = (
                    payload.get("details").get("data").get("bank_code")
                )

                amount = payload.get("details").get("data").get("amount")
                payer_account_name = (
                    payload.get("details").get("data").get("originatorname")
                )
                payer_account_number = (
                    payload.get("details").get("data").get("originatoraccountnumber")
                )
                # payer_bank_code = payload.get("details").get("data").get("originatorcbncode")

                narration = payload.get("details").get("data").get("narration")
                # session_id = payload.get("details").get("data").get("sessionid")
                # status = payload.get("details").get("data").get("statusmessage")

                transaction_instance = TransferRequest.objects.create(
                    company=company,
                    status=Status.SUCCESS
                    if status_meassage.lower() == "success"
                    else Status.FAILED,
                    destination_institution_code=recipient_bank_code,
                    beneficiary_account_name=recipient_account_name,
                    beneficiary_account_number=recipient_account_number,
                    # beneficiary_kyc_level=,
                    originator_account_name=payer_account_name,
                    originator_account_number=payer_account_number,
                    # originator_kyc_level=,
                    narration=narration,
                    transaction_final_amount=amount,
                    amount=amount,
                    reference=reference,
                    transaction_type=TransactionType.CREDIT,
                    payload=payload,
                )

                # Create General Transaction Detail instance
                transaction_detail = TransactionDetail.objects.create(
                    company=company,
                    beneficiary_account_number=recipient_account_number,
                    beneficiary_account_name=recipient_account_name,
                    amount=amount,
                    fee=0.0,
                    amount_payable=0.00,
                    transaction_type=TransactionType.CREDIT,
                    transaction_status=TransactionStatus.SUCCESSFUL
                    if status_meassage.lower() == "success"
                    else TransactionStatus.FAILED,
                    narration=narration,
                    service_provider=ServiceProvider.FIDELITY,
                    mode=api_mode,
                    bank_code=recipient_bank_code,
                    source_account=payer_account_number,
                    company_reference=str(company.id),
                )

                query.is_processed = True
                query.status = (
                    Status.SUCCESS
                    if status_meassage.lower() == "success"
                    else Status.FAILED
                )

                query.transaction_id = transaction_instance.request_ref

                if status_meassage.lower() == "success":
                    # FUND CUSTOMER ACCOUNT
                    fund_virtual_wallet = AccountSystem.fund_customer_account(
                        account=customer_account,
                        amount=amount,
                    )

                    if fund_virtual_wallet:
                        query.virtual_wallet_funded = True
                        query.save()

                    # Fund Company Account
                    fund_main_wallet = AccountDetail.fund_account(
                        company=customer_account.company,
                        account_type=AccountType.MAIN,
                        amount=float(amount),
                        charges=0.00,
                        sub_company=customer_account.sub_company,
                        provider=ServiceProvider.FIDELITY,
                    )

                    if fund_main_wallet.get("status"):
                        query.main_wallet_funded = True
                        query.save()

                        transaction_detail.balance_before = fund_main_wallet.get(
                            "previous_balance"
                        )
                        transaction_detail.balance_after = fund_main_wallet.get(
                            "account_balance"
                        )
                        transaction_detail.save()
                email_notify_owner_on_inflow(transaction_instance)
        self.message_user(request, "Process completed")

    def notify_companies_on_inflow(self, request, queryset):
        for query in queryset:
            query: AccountInflowPayload

            if query.is_processed and query.owner_notified is False:
                pass
            else:
                continue

            payload = ast.literal_eval(query.payload)

            if payload.get("details", {}).get("status", "").lower() == "successful":
                customer_ref = payload.get("details").get("customer_ref")

                try:
                    customer_account = AccountSystem.objects.get(
                        customer_ref=uuid.UUID(customer_ref)
                    )
                except AccountSystem.DoesNotExist:
                    continue

                company = customer_account.company
                company_callback_url = company.fidelity_callback_url

                reference = payload.get("request_ref")
                recipient_account_name = (
                    payload.get("details").get("data").get("craccountname")
                )
                recipient_account_number = (
                    payload.get("details").get("data").get("account_number")
                )
                amount = payload.get("details").get("data").get("amount")
                payer_account_name = (
                    payload.get("details").get("data").get("originatorname")
                )
                payer_account_number = (
                    payload.get("details").get("data").get("originatoraccountnumber")
                )
                payer_bank_code = (
                    payload.get("details").get("data").get("originatorcbncode")
                )
                narration = payload.get("details").get("data").get("narration")
                session_id = payload.get("details").get("data").get("sessionid")
                status = payload.get("details").get("data").get("statusmessage")
                paid_at = payload.get("details").get("data").get("trandate")

                request_data = {
                    "company": str(company.id),
                    "reference": reference,
                    "recipient_account_name": recipient_account_name,
                    "recipient_account_number": recipient_account_number,
                    "amount": amount,
                    "payer_account_name": payer_account_name,
                    "payer_account_number": payer_account_number,
                    "payer_bank_code": payer_bank_code,
                    "paid_at": paid_at,
                    "narration": narration,
                    "transaction_reference": reference,
                    "session_id": session_id,
                    "fee": "0.00",
                    "amount_payable": amount,
                    "settlement_status": True if status.lower() == "success" else False,
                    "currency": "NGN",
                    "source": "FIDELITY",
                }

                try:
                    headers = {"content-type": "application/json"}
                    resp = requests.request(
                        "POST",
                        url=company_callback_url,
                        headers=headers,
                        data=json.dumps(request_data),
                    )
                    response = resp.text

                    if resp.status_code == 200:
                        query.owner_notified = True
                except Exception as e:
                    response = str(e)
                query.company_notification_feedback = response
                query.save()

                transaction_detail_instance = TransactionDetail.objects.filter(
                    session_id=session_id
                ).last()
                transaction_detail_instance.company_event_response = response
                transaction_detail_instance.save()
            else:
                continue

        self.message_user(request, "Process completed")

    def process_pending_inflow_wallet_topup(self, request, queryset):
        """
        In the event of a inflow, part of the resolution flow it to fund the main wallet of the
        company assigned the wallet and also the wallet virtual account.
        Where this fails to occur as expected, this method is used to do a
        manual update.
        """
        for query in queryset:
            query: AccountInflowPayload

            if (
                query.is_processed
                and query.main_wallet_funded is False
                and query.virtual_wallet_funded is False
            ):
                pass
            else:
                continue

            payload = ast.literal_eval(query.payload)

            if payload.get("details", {}).get("status", "").lower() == "successful":
                customer_ref = payload.get("details").get("customer_ref")

                try:
                    customer_account = AccountSystem.objects.get(
                        customer_ref=uuid.UUID(customer_ref)
                    )
                except AccountSystem.DoesNotExist:
                    continue

                if customer_account.cash_balance > 0:
                    continue

                status_meassage = (
                    payload.get("details").get("data").get("statusmessage")
                )
                amount = payload.get("details").get("data").get("amount")

                if status_meassage.lower() == "success":
                    # FUND CUSTOMER ACCOUNT
                    fund_virtual_wallet = AccountSystem.fund_customer_account(
                        account=customer_account,
                        amount=amount,
                    )

                    if fund_virtual_wallet:
                        query.virtual_wallet_funded = True
                        query.save()

                    # Fund Company Account
                    fund_main_wallet = AccountDetail.fund_account(
                        company=customer_account.company,
                        account_type=AccountType.MAIN,
                        amount=float(amount),
                        charges=0.00,
                        sub_company=customer_account.sub_company,
                        provider=ServiceProvider.FIDELITY,
                    )

                    if fund_main_wallet.get("status"):
                        query.main_wallet_funded = True
                        query.save()
        self.message_user(request, "Process completed")

    notify_companies_on_inflow.allow_tags = True
    notify_companies_on_inflow.short_message = "FIDELITY: NOTIFY COMPANIES ON INFLOWS"

    process_inflow_transaction.allow_tags = True
    process_inflow_transaction.short_message = "FIDELITY: RESOLVE PENDING INFLOWS"

    process_pending_inflow_wallet_topup.allow_tags = True
    process_pending_inflow_wallet_topup.short_message = (
        "FIDELITY: COMPLETE PENDING INFLOWS TOPUP"
    )

    actions = [
        notify_companies_on_inflow,
        process_inflow_transaction,
        process_pending_inflow_wallet_topup,
    ]


class TransferRequestResourceAdmin(ImportExportModelAdmin):
    resource_class = TransferRequestResource
    list_filter = ["created_at", "transaction_type"]
    search_fields = ["company", "reference", "transaction_ref"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def resolve_pending_transactions(self, request, queryset):
        from fidelity.helpers.helpers import FidelityClass

        for query in queryset:
            query: TransferRequest

            if query.status == Status.PENDING and query.transaction_ref:
                check_trans = FidelityClass.check_transaction_status(
                    transaction_ref=query.transaction_ref
                )

                if check_trans.get("data", {}).get("status").lower() == "successful":
                    query.status = Status.SUCCESS
                    if query.transaction_detail_instance:
                        query.transaction_detail_instance.transaction_status = (
                            TransactionStatus.SUCCESSFUL
                        )
                elif check_trans.get("data", {}).get("status").lower() == "failed":
                    query.status = Status.FAILED
                    if query.transaction_detail_instance:
                        query.transaction_detail_instance.transaction_status = (
                            TransactionStatus.FAILED
                        )
                    try:
                        # Perform Fidelity Reversal
                        reversal = AccountSystem.perform_transfer_reversal(
                            query=query,
                            service_provider=ServiceProvider.FIDELITY,
                        )
                    except Exception as e:
                        print(e)
                else:
                    pass

                query.verification_response = check_trans
                query.save()
                query.transaction_detail_instance.save()

        self.message_user(request, "Resolved successfully")

    def create_parallel_transaction_details_record(self, request, queryset):
        """
        This is to create a corresponding accounts.
        TransactionDetail instance for already existing transactions
        """
        for query in queryset:
            query: TransferRequest

            if settings.ENVIRONMENT == "development":
                api_mode = APIMode.TEST
            else:
                api_mode = APIMode.LIVE

            if query.transaction_detail_instance:
                continue
            else:
                # Create Transaction Detail Instance
                transaction_detail_instance = TransactionDetail.objects.create(
                    company=query.company,
                    beneficiary_account_number=query.beneficiary_account_number or "",
                    beneficiary_account_name=query.beneficiary_account_name or "",
                    amount=query.amount,
                    fee=0.0,
                    amount_payable=0.00,
                    transaction_type=query.transaction_type,
                    transaction_status=TransactionStatus.SUCCESSFUL
                    if query.status == Status.SUCCESS
                    else TransactionStatus.FAILED,
                    narration=query.narration or "",
                    service_provider=ServiceProvider.FIDELITY,
                    mode=api_mode,
                    bank_code=query.destination_institution_code or "",
                    source_account=query.originator_account_number or "",
                    company_reference=str(query.company.id),
                )

                query.transaction_detail_instance = transaction_detail_instance
                query.save()
        self.message_user(request, "Resolved successfully")

    resolve_pending_transactions.allow_tags = True
    resolve_pending_transactions.short_description = (
        "Transaction: Check Transactions Status"
    )

    create_parallel_transaction_details_record.allow_tags = True
    create_parallel_transaction_details_record.short_description = (
        "Transaction: Create Transaction Detail Record"
    )

    actions = [
        resolve_pending_transactions,
        create_parallel_transaction_details_record,
    ]


class CallbackNotificationLogResourceAdmin(ImportExportModelAdmin):
    resource_class = CallbackNotificationLogResource
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class FidelityRequestLogResourceAdmin(ImportExportModelAdmin):
    resource_class = FidelityRequestLogResource
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(AccountSystem, AccountSystemResourceAdmin)
admin.site.register(TransferRequest, TransferRequestResourceAdmin)
admin.site.register(AccountInflowPayload, AccountInflowPayloadResourceAdmin)
admin.site.register(CallbackNotificationLog, CallbackNotificationLogResourceAdmin)
admin.site.register(FidelityRequestLog, FidelityRequestLogResourceAdmin)
