from datetime import date, datetime
import json
import uuid

from celery import shared_task
from django.conf import settings
import pytz

from helpers.enums import (
    APIMode,
    InflowStatus,
    ServiceProvider,
    TransactionStatus,
    TransactionType,
)
from helpers.reusable import (
    email_sender,
    make_request,
    is_valid_string,
)
from user_profiles.models import SubCompany
from wema_bank.helper.api_specifications import WemaBank
from wema_bank.helper.functions import (
    generate_unique_code,
)
from wema_bank.models import (
    ConstantVariable,
    SourceAccountBalance,
    TransactionCallback,
    VirtualAccount,
)


# Create your task(s) here.
@shared_task
def send_company_transaction_callbacks(transaction_id: str):
    """
    Send transaction callback (event) to a company based on recipient account information.
    Args:
        transaction_id: it represents the verified TransactionDetail instance ID.
    Returns:
        str: A message indicating the status of the callback request.
    """
    from accounts.models import TransactionDetail

    get_transaction = TransactionDetail.objects.filter(
        id=transaction_id
    ).last()
    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Hook {settings.CALLBACK_BEARER}",
    }
    company = get_transaction.company
    sub_company = get_transaction.sub_company
    session_id = get_transaction.session_id
    one_time = get_transaction.one_time
    request_reference = get_transaction.request_reference
    if sub_company is not None:
        sub_company_url = sub_company.callback_url
        if sub_company_url is None:
            default_message = "sub company has no callback URL set."
            get_transaction.event_sent = True
            get_transaction.event_send_count += 1
            get_transaction.event_updated_at = TODAY
            get_transaction.company_event_response = {
                "details": default_message}
            get_transaction.save()
        else:
            data = json.dumps(
                {
                    "one_time": one_time,
                    "request_reference": request_reference,
                    "company": sub_company.company_name,
                    "recipient_account_name": get_transaction.beneficiary_account_name,
                    "recipient_account_number": get_transaction.beneficiary_account_number,
                    "amount": float(get_transaction.amount),
                    "fee": float(get_transaction.fee),
                    "amount_payable": float(get_transaction.amount_payable),
                    "reference": str(transaction_id),
                    "transaction_type": get_transaction.transaction_type,
                    "payer_account_name": get_transaction.source_name,
                    "payer_account_number": get_transaction.source_account,
                    "payer_bank_code": get_transaction.bank_code,
                    "paid_at": str(get_transaction.created_at),
                    "narration": get_transaction.narration,
                    "session_id": session_id,
                    "transaction_reference": get_transaction.reference,
                    "settlement_status": get_transaction.settled,
                    "currency": get_transaction.currency,
                    "provider": ServiceProvider.WEMA_BANK,
                }
            )
            response = make_request(
                "POST", dict(url=sub_company_url, headers=headers, data=data)
            )
            if response.get("status_code") == 200:
                get_transaction.event_sent = True
                get_transaction.company_event_response = json.dumps(
                    response.get("data")
                )
            else:
                get_transaction.company_event_response = json.dumps(
                    response.get("data")
                )
            get_transaction.event_send_count += 1
            get_transaction.event_updated_at = TODAY
            get_transaction.save()
            return response

    company_url = company.callback_url
    if company_url is None:
        default_message = "company has no callback URL set."
        get_transaction.event_sent = True
        get_transaction.event_send_count += 1
        get_transaction.event_updated_at = TODAY
        get_transaction.company_event_response = {"details": default_message}
        get_transaction.save()
        return default_message
    else:
        data = json.dumps(
            {
                "one_time": one_time,
                "request_reference": request_reference,
                "company": company.name,
                "sub_company": (
                    sub_company.company_name if sub_company is not None else None
                ),
                "sub_company_email": (
                    sub_company.company_email if sub_company is not None else None
                ),
                "sub_company_unique_id": (
                    sub_company.unique_id if sub_company is not None else None
                ),
                "recipient_account_name": get_transaction.beneficiary_account_name,
                "recipient_account_number": get_transaction.beneficiary_account_number,
                "amount": float(get_transaction.amount),
                "fee": float(get_transaction.fee),
                "amount_payable": float(get_transaction.amount_payable),
                "reference": str(transaction_id),
                "transaction_type": get_transaction.transaction_type,
                "payer_account_name": get_transaction.source_name,
                "payer_account_number": get_transaction.source_account,
                "payer_bank_code": get_transaction.bank_code,
                "paid_at": str(get_transaction.created_at),
                "narration": get_transaction.narration,
                "session_id": session_id,
                "transaction_reference": get_transaction.reference,
                "settlement_status": get_transaction.settled,
                "currency": get_transaction.currency,
                "provider": ServiceProvider.WEMA_BANK,
            }
        )
        response = make_request(
            "POST", dict(url=company_url, headers=headers, data=data)
        )
        if response.get("status_code") == 200:
            get_transaction.event_sent = True
            get_transaction.company_event_response = json.dumps(
                response.get("data")
            )
        else:
            get_transaction.company_event_response = json.dumps(
                response.get("data")
            )
        get_transaction.event_send_count += 1
        get_transaction.event_updated_at = TODAY
        get_transaction.save()
        return response


@shared_task
def resend_company_transaction_callbacks():
    from accounts.models import TransactionDetail

    # Successful inflows only (ignore reversals).
    pending_events = TransactionDetail.objects.filter(
        transaction_status=TransactionStatus.SUCCESSFUL,
        transaction_type=TransactionType.CREDIT,
        event_sent=False,
        event_send_count__lte=4,
    )
    if pending_events.exists():
        for event in pending_events:
            send_company_transaction_callbacks.delay(event.id)
        return "EVENTS RETRIED SUCCESSFULLY."
    return "THERE ARE NO PENDING EVENTS."


@shared_task
def float_account_balance():
    balance_details = WemaBank.get_balance()
    if balance_details.get("status"):
        source_account_record = SourceAccountBalance.objects.filter(
            created_at__date=date.today()
        ).first()
        if source_account_record is None:
            SourceAccountBalance.objects.create(
                opening_balance=float(balance_details.get("wema_response"))
            )
        else:
            source_account_record.closing_balance = float(
                balance_details.get("wema_response")
            )
            source_account_record.save()
        return "SUCCESSFULLY UPDATED BALANCES."
    else:
        return balance_details.get("message")


@shared_task
def retry_inflow_verification():
    from accounts.tasks import handle_wema_company_collection

    inflow_transactions = TransactionCallback.objects.filter(
        status=InflowStatus.UNCONFIRMED
    )
    if inflow_transactions.exists():
        for inflow in inflow_transactions:
            if is_valid_string(inflow.session_id):
                if inflow.is_resolved:
                    transaction_status = WemaBank.inflow_event_tsq(
                        session_id=inflow.session_id
                    )
                    if not transaction_status.get("status"):
                        inflow.failed_tsq_payload = json.dumps(
                            transaction_status
                        )
                        inflow.tsq_count += 1
                        inflow.save()
                    else:
                        data = transaction_status.get("data")
                        if data.get("status") == "00":
                            response_data = data.get("transactions")[0]
                            response_session_id = response_data.get(
                                "sessionid"
                            )
                            response_craccount = response_data.get("craccount")
                            response_amount = float(
                                response_data.get("amount"))
                            if (
                                inflow.session_id == response_session_id and (
                                    inflow.recipient_account_number == response_craccount
                                    and inflow.amount == response_amount
                                )
                            ):
                                inflow.step_two = True
                                inflow.status = InflowStatus.CONFIRMED
                                inflow.tsq_count += 1
                                inflow.confirmed_at = datetime.now(
                                    tz=pytz.timezone(settings.TIME_ZONE)
                                )
                                inflow.confirmation_payload = json.dumps(data)
                                inflow.save()
                                allow_inflow = True
                            else:
                                inflow.status = InflowStatus.INVALID
                                inflow.tsq_count += 1
                                inflow.confirmed_at = datetime.now(
                                    tz=pytz.timezone(settings.TIME_ZONE)
                                )
                                inflow.confirmation_payload = json.dumps(data)
                                inflow.save()
                else:
                    handle_wema_company_collection.delay(inflow_id=inflow.id)
        return "PENDING INFLOWS SUCCESSFULLY RETRIED."
    return "THERE ARE NO UNCONFIRMED INFLOWS."


@shared_task
def register_offline_accounts(sub_company_id: str):
    sub_company = SubCompany.objects.filter(id=sub_company_id).first()
    if sub_company is not None:
        offline_accounts = VirtualAccount.objects.filter(
            offline=True,
            sub_company=None,
        )
        count = 1
        for account in offline_accounts:
            unique_code = generate_unique_code()
            unique_code_checker = VirtualAccount.objects.filter(
                unique_code=unique_code
            )
            confirmation_code = generate_unique_code()
            confirmation_code_checker = VirtualAccount.objects.filter(
                confirmation_code=confirmation_code
            )
            if unique_code_checker.exists() and confirmation_code_checker.exists():
                pass
            account.sub_company = sub_company
            account.first_name = sub_company.company_name
            account.unique_code = unique_code
            account.confirmation_code = confirmation_code
            account.save()

            count += 1
            if count == 500:
                break
        return True


@shared_task
def unconfirmed_inflow_reminder():
    unconfirmed_inflow = TransactionCallback.objects.filter(
        is_resolved=False, status=InflowStatus.UNCONFIRMED
    )
    if unconfirmed_inflow.exists():
        staff_emails = ConstantVariable.objects.first().notify_email.split(",")
        email_response = email_sender(
            recipient=staff_emails,
            subject="Important: Wema Bank Unconfirmed Inflow",
            template_directory="inflow_reminder.html",
            use_template=True,
            inflow_count=unconfirmed_inflow.count(),
        )
        return str(email_response)
    return "NO PENDING UNCONFIRMED INFLOW ALERT."


@shared_task
def instant_payout(transaction_id: str):
    """
    Wema Bank Virtual Account Instant Payout.
    """
    from accounts.models import TransactionDetail, TransferMoneyRequest

    credit_transaction = TransactionDetail.objects.filter(
        id=transaction_id
    ).last()
    account_details = VirtualAccount.objects.filter(
        account_number=credit_transaction.beneficiary_account_number
    ).last()
    if account_details.sub_company is not None:
        source_account = account_details.sub_company.nuban
    else:
        source_account = account_details.company.nuban
    # Register the payout transaction.
    payout_transaction = TransferMoneyRequest.register_transfer_request(
        company=credit_transaction.company,
        source_account=source_account,
        mode=APIMode.LIVE,
        account_name=account_details.settlement_account_name,
        account_number=account_details.settlement_account_number,
        bank_code=account_details.settlement_bank_code,
        request_reference=str(uuid.uuid4()),
        amount=float(credit_transaction.amount_payable),
        narration=credit_transaction.narration,
    )
    if payout_transaction.get("status"):
        credit_transaction.settled = True
        credit_transaction.save()
    return f"INSTANT PAYOUT SUCCESSFULLY TRIED for {credit_transaction.session_id}."
