from django.urls import include, path

from wema_bank import views


# Create your urls pattern(s) here.
wema_bank_urls = [
    path("account_lookup", views.WemaLookUpAPIView.as_view()),
    path("mini_statement", views.MiniStatementAPIView.as_view()),
    path("kyc_details", views.KYCDetailsAPIView.as_view()),
    path("block_account", views.BlockAccountAPIView.as_view()),

]

urlpatterns = [
    path("", include(wema_bank_urls)),
    path(
        "virtual_accounts/",
        views.VirtualAccountAPIView.as_view(),
        name="create_virtual_account",
    ),
    path("app2/virtual_accounts/", views.VirtualAccountV2APIView.as_view()),
    path("verify_event", views.VerifyEventAPIView.as_view()),
    path("app3/virtual_accounts/", views.VirtualAccountV3APIView.as_view()),
    path("instant_accounts/", views.InstantAccountAPIView.as_view()),
    # NOTE: use path below to create inflow record manually.
    path("manual_inflow_update/", views.ManualWemaInflowAPIView.as_view()),
    path("offline_accounts/", views.OfflineAccountsAPIView.as_view()),
    path("ussd_verify", views.USSDVerifyAPIView.as_view()),
    path("nip_details", views.NIPStatusAPIView.as_view()),
]
