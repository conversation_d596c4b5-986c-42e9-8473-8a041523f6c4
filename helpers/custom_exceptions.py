from rest_framework import status
from rest_framework.exceptions import APIException
from rest_framework.views import exception_handler

from helpers.custom_responses import Response


# Create your custom exception(s) here.
def custom_exception_handler(exception, context):
    """
    Custom Exception Handler for Django REST framework.
    NOTE:
    This function is designed to be used as a custom exception handler in Django REST framework.
    It takes two parameters, `exception` (the raised exception) and `context` (context of the exception).
    The function calls the default exception handler (`exception_handler`) to get the response.
    If the response is not None, it extracts the status code from the response.
    Then, it creates a new `Response` object with the errors data from the original response,
    along with the extracted status code and status, and returns it.
    Args:
        exception (Exception): The raised exception object.
        context (dict): The context of the exception.
    Returns:
        Response: A new Response object with error data and status code from the original response.
    """
    response = exception_handler(exception, context)
    if response is not None:
        status_code = response.status_code
    return Response(
        errors=response.data,
        status_code=status_code,
        status=response.status_code,
    )


class BearerAccessException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {"message": "unauthorized."}
    default_code = "unauthorized"


class CompanyCanSendMoneyException(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "message": "company is not provisioned for this service."
    }
    default_code = "forbidden"


class CompanyVerificationException(APIException):
    status_code = status.HTTP_412_PRECONDITION_FAILED
    default_detail = {"message": "company is not verified."}
    default_code = "precondition failed"


class WhitelistedIPAddressException(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {"message": "IP address is not whitelisted."}
    default_code = "forbidden"


class AdminException(APIException):
    status_code = status.HTTP_412_PRECONDITION_FAILED
    default_detail = {"message": "admin access is required."}
    default_code = "forbidden"
