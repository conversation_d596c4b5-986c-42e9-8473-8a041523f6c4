from django.conf import settings
from rest_framework import permissions

from core.models import WhitelistedIPAddress
from helpers import custom_exceptions
from helpers.reusable import get_ip_address
from user_profiles.models import API<PERSON>ey


# Create your custom permission(s) here.
class APIAccessPermission(permissions.BasePermission):
    """
    Grants access to known API key(s).
    """

    def has_permission(self, request, view):
        auth_key = request.META.get("HTTP_AUTHORIZATION")
        if not auth_key:
            return False
        user = APIKey.verify(auth_key)
        if isinstance(user, str):
            return False
        return True


class CompanyCanSendMoney(permissions.BasePermission):
    """
    Grants access to a company provisioned for the service.
    """

    def has_permission(self, request, view):
        company = request.user
        if not company.is_authenticated:
            return False
        if company.can_send_money:
            return True
        raise custom_exceptions.CompanyCanSendMoneyException


class WemaIsAuthenticated(permissions.IsAuthenticated):
    """
    Grants access to Wema Bank Plc.
    """

    def has_permission(self, request, view):
        authorization = request.META.get("HTTP_AUTHORIZATION")
        if authorization:
            bearer_token = authorization.split()[1]
            if bearer_token == settings.WEMA_BEARER:
                return True
            raise custom_exceptions.BearerAccessException
        return False


class IsCompanyVerified(permissions.BasePermission):
    """
    Grants access only to a verified company.
    """

    def has_permission(self, request, view):
        company = request.user
        if not company.is_authenticated:
            return False
        if company.is_verified:
            return True
        raise custom_exceptions.CompanyVerificationException


class IsIPWhitelisted(permissions.BasePermission):
    """
    Grants access only to known IP address(es).
    """

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        ip_address = get_ip_address(request=request)
        validate_ip_address = WhitelistedIPAddress.validate_ip(
            company=request.user, ip_address=ip_address
        )
        if validate_ip_address:
            return True
        raise custom_exceptions.WhitelistedIPAddressException


class USSDIsAuthenticated(permissions.IsAuthenticated):
    """
    Grants access to USSD service.
    """

    def has_permission(self, request, view):
        authorization = request.META.get("HTTP_AUTHORIZATION")
        if authorization:
            bearer_token = authorization.split()[1]
            if bearer_token == settings.USSD_BEARER:
                return True
            raise custom_exceptions.BearerAccessException
        return False


class IsAdmin(permissions.BasePermission):
    """
    Grants access only to a verified company.
    """

    def has_permission(self, request, view):
        if request.user.is_admin:
            return True
        raise custom_exceptions.AdminException


class IsSharedBearer(permissions.IsAuthenticated):
    """
    Grants access to known App(s).
    """

    def has_permission(self, request, view):
        authorization = request.META.get("HTTP_AUTHORIZATION")
        if authorization:
            bearer_token = authorization.split()[1]
            if bearer_token == settings.SHARED_BEARER:
                return True
            raise custom_exceptions.BearerAccessException
        return False


class CompanyCanDisburseLoan(permissions.BasePermission):
    """
    Grants access to a company provisioned for the service.
    """

    def has_permission(self, request, view):
        if settings.ENVIRONMENT != "development":
            loan_companies_emails = ["<EMAIL>"]
        else:
            loan_companies_emails = ["<EMAIL>", "<EMAIL>"]
        company = request.user
        if not company.is_authenticated:
            return False
        if company.email in loan_companies_emails:
            return True
        raise custom_exceptions.CompanyCanSendMoneyException