from django.db.models import TextChoices


# Create your enumeration type(s) here.
class AccountType(TextChoices):
    COMMISSION = "COMMISSION", "COMMISSION"
    MAIN = "MAIN", "MAIN"
    SUB = "SUB", "SUB"


class APIMode(TextChoices):
    LIVE = "LIVE", "LIVE"
    TEST = "TEST", "TEST"


class Currency(TextChoices):
    NGN = "NGN", "NGN"
    USD = "USD", "USD"


class InflowStatus(TextChoices):
    CONFIRMED = "CONFIRMED", "CONFIRMED"
    INVALID = "INVALID", "INVALID"
    MANUAL_CONFIRMATION = "MANUAL_CONFIRMATION", "MANUAL_CONFIRMATION"
    UNCONFIRMED = "UNCONFIRMED", "UNCONFIRMED"


class OTPType(TextChoices):
    REGISTRATION = "REGISTRATION", "REGISTRATION"
    RESET = "RESET", "RESET"
    TRANSACTION = "TRANSACTION", "TRANSACTION"


class TransactionStatus(TextChoices):
    AWAITING_CONFIRMATION = "AWAITING_CONFIRMATION", "AWAITING_CONFIRMATION"
    FAILED = "FAILED", "FAILED"
    IN_PROGRESS = "IN_PROGRESS", "IN_PROGRESS"
    PENDING = "PENDING", "PENDING"
    REVERSED = "REVERSED", "REVERSED"
    SUCCESSFUL = "SUCCESSFUL", "SUCCESSFUL"


class TransactionType(TextChoices):
    CREDIT = "CREDIT", "CREDIT"
    DEBIT = "DEBIT", "DEBIT"


class ServiceProvider(TextChoices):
    WEMA_BANK = "WEMA_BANK", "WEMA_BANK",
    ZENITH_BANK = "ZENITH_BANK", "ZENITH_BANK"
    CASH_CONNECT = "CASH_CONNECT", "CASH_CONNECT"
    FIDELITY = "FIDELITY", "FIDELITY"
    CORE_BANKING = "CORE_BANKING", "CORE_BANKING"


class UserTitle(TextChoices):
    MR = "MR", "MR"
    MRS = "MRS", "MRS"
    MISS = "MISS", "MISS"


class UserGender(TextChoices):
    MALE = "MALE", "MALE"
    FEMALE = "FEMALE", "FEMALE"


class UserMaritalStatus(TextChoices):
    DIVORCED = "DIVORCED", "DIVORCED"
    SINGLE = "SINGLE", "SINGLE"
    MARRIED = "MARRIED", "MARRIED"
    WINDOWED = "WINDOWED",  "WINDOWED"
    SEPARATED = "SEPARATED", "SEPARATED"


class CashConnectTypeOfVirtualAccount(TextChoices):
    VIRTUAL = "VIRTUAL", "VIRTUAL"
    INSTANT = "INSTANT", "INSTANT"


class CashConnectRquestTypes(TextChoices):
    VIRTUAL_ACCOUNT = "VIRTUAL_ACCOUNT", "VIRTUAL_ACCOUNT"
    VIRTUAL_ACCOUNT_BY_BVN = "VIRTUAL_ACCOUNT_BY_BVN", "VIRTUAL_ACCOUNT_BY_BVN"
    VIRTUAL_ACCOUNT_BY_NIN = "VIRTUAL_ACCOUNT_BY_NIN", "VIRTUAL_ACCOUNT_BY_NIN"
    INSTANT_VIRTUAL_ACCOUNT = "INSTANT_VIRTUAL_ACCOUNT", "INSTANT_VIRTUAL_ACCOUNT"
    CORPORATE_ACCOUNT = "CORPORATE_ACCOUNT", "CORPORATE_ACCOUNT"
    INTER_DISBURSEMENT = "INTER_DISBURSEMENT", "INTER_DISBURSEMENT"
    INTRA_DISBURSEMENT = "INTRA_DISBURSEMENT", "INTRA_DISBURSEMENT"
    DISBURSEMENT_VERIFICATION = "DISBURSEMENT_VERIFICATION", "DISBURSEMENT_VERIFICATION"
    ACCOUNT_NAME_LOCKUP = "ACCOUNT_NAME_LOCKUP", "ACCOUNT_NAME_LOCKUP"
    BOOK_LOAN = "BOOK_LOAN", "BOOK_LOAN"
    FETCH_LOAN_TYPES = "FETCH_LOAN_TYPES", "FETCH_LOAN_TYPES"
    LOAN_DISBURSEMENT = "LOAN_DISBURSEMENT", "LOAN_DISBURSEMENT"
    FETCH_LOAN_SCHEDULE = "FETCH_LOAN_SCHEDULE", "FETCH_LOAN_SCHEDULE"
    FETCH_RUNTIME_PARAMS = "FETCH_RUNTIME_PARAMS", "FETCH_RUNTIME_PARAMS"
    CHECK_BALANCE = "CHECK_BALANCE", "CHECK_BALANCE"


class TypeOfId(TextChoices):
    NIN = "NIN", "NIN"
    BVN = "BVN", "BVN"


class TransferMode(TextChoices):
    EXTERNAL = "EXTERNAL", "EXTERNAL"
    INTERNAL = "INTERNAL", "INTERNAL"
