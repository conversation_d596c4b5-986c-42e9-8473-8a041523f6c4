from django.conf import settings
from one_pipe.helpers.func import Onepipe
import requests

from one_pipe.models import ConstantTable, Mandate, Payment


class OnepipePayWithAccount:
    url = "https://api.onepipe.io"
    ONEPIPE_API_KEY = settings.ONEPIPE_API_KEY


    @classmethod
    def get_banks(cls, validated_data, company):
        from one_pipe.models import PayWithAccount

        email = validated_data.get("email")
        phone_number = validated_data.get("phone_number") 
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        
        constant_variable = ConstantTable.get_constant_variable()
        if not constant_variable:
            mock_mode = "Inspect"
            on_off = False
        else:
            mock_mode = constant_variable.mock_mode
            on_off = constant_variable.on_off
        

        url = f"{cls.url}/v2/transact"
        ONEPIPE_API_KEY = cls.ONEPIPE_API_KEY
        request_ref = Onepipe.generate_request_ref()
        transaction_ref = Onepipe.generate_transaction_ref()
        signature = Onepipe.signature(request_ref=request_ref)

        
        if on_off is True:
            headers = {
                "Authorization": f"Bearer {ONEPIPE_API_KEY}",
                "Signature": f"{signature}",
                "Content-Type": "application/json"
            }
            payload = {
                "request_ref": f"{request_ref}",
                "request_type": "get_banks",
                "auth": {
                    "type": None,
                    "secure": None,
                    "auth_provider": "PaywithAccount",
                    "route_mode": None
                },
                "transaction": {
                    "mock_mode": f"{mock_mode}",
                    "transaction_ref": f"{transaction_ref}",
                    "transaction_desc": "A random transaction",
                    "transaction_ref_parent": None,
                    "amount": 0,
                    "customer": {
                    "customer_ref": f"{phone_number}",
                    "firstname": f"{first_name}",
                    "surname": f"{last_name}",
                    "email": f"{email}",
                    "mobile_no": f"{phone_number}"
                    },
                    "meta": {
                    "pwa_enabled_only": True
                    },
                    "details": None
                }
            }
            print(payload)
            pay_with_account = PayWithAccount.objects.create(
                request_type="GET_BANKS",
                company=company,
                mock_mode=mock_mode,
                request_ref=request_ref,
                transaction_ref=transaction_ref,
                request=payload,
            )
            response = requests.request("POST", url, headers=headers, json=payload)
            response_data = response.json()

            pay_with_account.response = response_data
            pay_with_account.save()
            return response_data
        else:
            response_data = {
                "status": "Failed", 
                "message": "Service is temporarily unavailable",
                "data": {
                    "provider_response_code": "400"
                }
            }
            return response_data
        
    @classmethod
    def lookup_account_max(cls, validated_data, company):
        from one_pipe.models import PayWithAccount, LookupAccount

        email = validated_data.get("email")
        phone_number = validated_data.get("phone_number")
        bvn_number = validated_data.get("bvn_number")
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        account_number = validated_data.get("account_number")
        bank_cbn_code = validated_data.get("bank_cbn_code")

        constant_variable = ConstantTable.get_constant_variable()
        if not constant_variable:
            mock_mode = "Inspect"
            on_off = False
        else:
            mock_mode = constant_variable.mock_mode
            on_off = constant_variable.on_off
        

        url = f"{cls.url}/v2/transact"
        ONEPIPE_API_KEY = cls.ONEPIPE_API_KEY
        request_ref = Onepipe.generate_request_ref()
        transaction_ref = Onepipe.generate_transaction_ref()
        signature = Onepipe.signature(request_ref=request_ref)
        encrypted_secure = Onepipe.encrypted_secure_bank_account(account_number=account_number, bank_cbn_code=bank_cbn_code)
        encrypted_bvn = Onepipe.encrypted_secure_bvn(bvn_number=bvn_number)

        
        if on_off is True:
            headers = {
                "Authorization": f"Bearer {ONEPIPE_API_KEY}",
                "Signature": f"{signature}",
                "Content-Type": "application/json"
            }
            payload = {
                "request_ref": f"{request_ref}",
                "request_type": "lookup account max",
                "auth": {
                    "type": "bank.account",
                    "secure": f"{encrypted_secure}",
                    "auth_provider": "PaywithAccount"
                },
                "transaction": {
                    "mock_mode": f"{mock_mode}",
                    "transaction_ref": f"{transaction_ref}",
                    "transaction_desc": "Verify account ownership",
                    "transaction_ref_parent": None,
                    "amount": 0,
                    "customer": {
                    "customer_ref": f"{phone_number}",
                    "firstname": f"{first_name}",
                    "surname": f"{last_name}",
                    "email": f"{email}",
                    "mobile_no": f"{phone_number}"
                    },
                    "meta": {
                        "bvn": f"{encrypted_bvn}"
                    },
                    "details": {}
                }
            }
            pay_with_account = PayWithAccount.objects.create(
                request_type="LOOK_UP_ACCOUNT",
                company=company,
                mock_mode=mock_mode,
                request_ref=request_ref,
                transaction_ref=transaction_ref,
                request=payload,
                bvn_number=bvn_number,
                account_number=account_number,
                bank_cbn_code=bank_cbn_code
            )
            response = requests.request("POST", url, headers=headers, json=payload)
            response_data = response.json()

            pay_with_account.response = response_data
            pay_with_account.save()
            
            if response.status_code == 200:
                data = response_data.get("data")
                if data.get("provider_response_code") == "00":
                    # Assuming the response contains a 'provider_response' field with the relevant data
                    provider_response = data.get("provider_response", {})
                    customer_id = provider_response.get("customer_id")
                    account_currency = provider_response.get("account_currency")
                    account_number = provider_response.get("account_number")
                    account_name = provider_response.get("account_name")
                    first_name = provider_response.get("first_name")
                    last_name = provider_response.get("last_name")
                    middle_name = provider_response.get("middle_name")
                    gender = provider_response.get("gender")
                    dob = provider_response.get("dob")
                    account_branch = provider_response.get("account_branch")
                    account_status = provider_response.get("account_status")
                    ledger_balance = provider_response.get("ledger_balance")
                    available_balance = provider_response.get("available_balance")
                    kyc_level = provider_response.get("kyc_level")
                    name_enquiry_id = provider_response.get("name_enquiry_id")
                    name_enquiry_id = provider_response.get("name_enquiry_id")
                    address1 = provider_response.get("address1")
                    address2 = provider_response.get("address2")
                    address3 = provider_response.get("address3")
                    email = provider_response.get("email").lower() if provider_response.get("email") else None
                    phone_number = provider_response.get("mobile_number")
                    account_type = provider_response.get("account_type")
                    meta = provider_response.get("meta")   

                    LookupAccount.objects.create(
                        email = email,
                        company=company,
                        phone_number = phone_number,
                        request_ref = request_ref,
                        transaction_ref = transaction_ref,
                        account_number = account_number,
                        account_name = account_name,
                        first_name = first_name,
                        last_name = last_name,
                        middle_name = middle_name,
                        gender = gender,
                        dob = dob,
                        mock_mode = mock_mode,
                        available_balance = available_balance,
                        kyc_level = kyc_level,
                        address1 = address1,
                        address2 = address2,
                        address3 = address3,
                        customer_id = customer_id,
                        account_currency = account_currency,
                        name_enquiry_id = name_enquiry_id,
                        account_branch = account_branch,
                        account_status = account_status,
                        ledger_balance = ledger_balance,
                        account_type = account_type,
                        meta = meta,
                        bvn_number = bvn_number
                    )
            return response_data
        else:
            response_data = {
                "status": "Failed", 
                "message": "Service is temporarily unavailable",
                "data": {
                    "provider_response_code": "400"
                }
            }
            return response_data
    
    @classmethod
    def create_mandate(cls, validated_data, company):
        from one_pipe.models import PayWithAccount

        email = validated_data.get("email")
        phone_number = validated_data.get("phone_number")
        bvn_number = validated_data.get("bvn_number")
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        amount = validated_data.get("amount")
        account_number = validated_data.get("account_number")
        bank_cbn_code = validated_data.get("bank_cbn_code")

        constant_variable = ConstantTable.get_constant_variable()
        if not constant_variable:
            mock_mode = "Inspect"
            on_off = False
        else:
            mock_mode = constant_variable.mock_mode
            on_off = constant_variable.on_off

        url = f"{cls.url}/v2/transact"
        ONEPIPE_API_KEY = cls.ONEPIPE_API_KEY
        request_ref = Onepipe.generate_request_ref()
        transaction_ref = Onepipe.generate_transaction_ref()
        signature = Onepipe.signature(request_ref=request_ref)
        encrypted_secure = Onepipe.encrypted_secure_bank_account(account_number=account_number, bank_cbn_code=bank_cbn_code)
        encrypted_bvn = Onepipe.encrypted_secure_bvn(bvn_number=bvn_number)
        amount_in_kobo = Onepipe.amount_in_kobo(amount)
        
        
        if on_off is True:
        
            headers = {
                "Authorization": f"Bearer {ONEPIPE_API_KEY}",
                "Signature": f"{signature}",
                "Content-Type": "application/json"
            }

            payload = {
                "request_ref": f"{request_ref}",
                "request_type": "create mandate",
                "auth": {
                    "type": "bank.account",
                    "secure": f"{encrypted_secure}",
                    "auth_provider": "PaywithAccount"
                },
                "transaction": {
                    "mock_mode": f"{mock_mode}",
                    "transaction_ref": f"{transaction_ref}",
                    "transaction_desc": "Creating a mandate",
                    "transaction_ref_parent": None,
                    "amount": 0,
                    "customer": {
                        "customer_ref": f"{phone_number}",
                        "firstname": f"{first_name}",
                        "surname": f"{last_name}",
                        "email": f"{email}",
                        "mobile_no": f"{phone_number}"
                    },
                    "meta": {
                        "amount": f"{amount_in_kobo}",
                        "skip_consent": "true",
                        "bvn": f"{encrypted_bvn}",
                        "biller_code" : "",
                        "customer_consent": ""
                    },
                    "details": {}
                }
            }

            pay_with_account = PayWithAccount.objects.create(
                request_type="CREATE_MANDATE",
                company=company,
                mock_mode=mock_mode,
                request_ref=request_ref,
                transaction_ref=transaction_ref,
                request=payload,
                bvn_number=bvn_number,
                account_number=account_number,
                bank_cbn_code=bank_cbn_code
            )
            response = requests.request("POST", url, headers=headers, json=payload)
            response_data = response.json()

            pay_with_account.response = response_data
            pay_with_account.save()
            
            if response.status_code == 200:
                data = response_data.get("data")
                if data.get("provider_response_code") == "00":
                    # Assuming the response contains a 'provider_response' field with the relevant data
                    provider_response = data.get("provider_response", {})
                    reference = provider_response.get("reference")
                    account_number = provider_response.get("account_number")
                    contract_code = provider_response.get("contract_code")
                    account_reference = provider_response.get("account_reference")
                    account_name = provider_response.get("account_name")
                    currency_code = provider_response.get("currency_code")
                    email = provider_response.get("customer_email")
                    bank_name = provider_response.get("bank_name")
                    bank_code = provider_response.get("bank_code")
                    account_type = provider_response.get("account_type")
                    status = provider_response.get("status")
                    created_on = provider_response.get("created_on")
                    meta = provider_response.get("meta")  

                    Mandate.objects.create(
                        email = email,
                        company=company,
                        reference = reference,
                        request_ref = request_ref,
                        transaction_ref = transaction_ref,
                        contract_code = contract_code,
                        phone_number = phone_number,
                        account_reference = account_reference,
                        bank_name = bank_name,
                        bank_code = bank_code,
                        currency_code = currency_code,
                        account_number = account_number,
                        account_name = account_name,
                        first_name = first_name,
                        last_name = last_name,
                        mock_mode = mock_mode,
                        status = status,
                        created_on = created_on,
                        account_type = account_type,
                        meta = meta,
                        amount = amount,
                        amount_in_kobo = amount_in_kobo,
                        bvn_number = bvn_number
                    )
            return response_data
        else:
            response_data = {
                "status": "Failed", 
                "message": "Service is temporarily unavailable",
                "data": {
                    "provider_response_code": "400"
                }
            }
            return response_data
    
    @classmethod
    def collect_payment(cls, validated_data, company):
        from one_pipe.models import PayWithAccount

        email = validated_data.get("email")
        phone_number = validated_data.get("phone_number")
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        amount = validated_data.get("amount")
        account_number = validated_data.get("account_number")
        bank_cbn_code = validated_data.get("bank_cbn_code")

        constant_variable = ConstantTable.get_constant_variable()
        if not constant_variable:
            mock_mode = "Inspect"
            on_off = False
        else:
            mock_mode = constant_variable.mock_mode
            on_off = constant_variable.on_off

        url = f"{cls.url}/v2/transact"
        ONEPIPE_API_KEY = cls.ONEPIPE_API_KEY
        request_ref = Onepipe.generate_request_ref()
        transaction_ref = Onepipe.generate_transaction_ref()
        signature = Onepipe.signature(request_ref=request_ref)
        encrypted_secure = Onepipe.encrypted_secure_bank_account(account_number=account_number, bank_cbn_code=bank_cbn_code)
        amount_in_kobo = Onepipe.amount_in_kobo(amount)
        
        if on_off is True:
        
            headers = {
                "Authorization": f"Bearer {ONEPIPE_API_KEY}",
                "Signature": f"{signature}",
                "Content-Type": "application/json"
            }
            payload = {
                "request_ref": f"{request_ref}",
                "request_type": "collect",
                "auth": {
                    "type": "bank.account",
                    "secure": f"{encrypted_secure}",
                    "auth_provider": "PaywithAccount"
                },
                "transaction": {
                    "mock_mode": f"{mock_mode}",
                    "transaction_ref": f"{transaction_ref}",
                    "transaction_desc": f"Collecting payment for {email}",
                    "transaction_ref_parent": None,
                    "amount": f"{amount_in_kobo}",
                    "customer": {
                        "customer_ref": f"{phone_number}",
                        "firstname": f"{first_name}",
                        "surname": f"{last_name}",
                        "email": f"{email}",
                        "mobile_no": f"{phone_number}"
                    },
                    "meta": {
                        "biller_code" : "000613",
                        "skip_consent": "true",
                        "customer_consent": ""
                    },
                    "details": {}
                }
            }

            pay_with_account = PayWithAccount.objects.create(
                request_type="COLLECT_PAYMENT",
                company=company,
                mock_mode=mock_mode,
                request_ref=request_ref,
                transaction_ref=transaction_ref,
                request=payload,
            )
            response = requests.request("POST", url, headers=headers, json=payload)
            response_data = response.json()

            pay_with_account.response = response_data
            pay_with_account.save()
            
            if response.status_code == 200:
                data = response_data.get("data")
                if data.get("provider_response_code") == "00":
                    # Assuming the response contains a 'provider_response' field with the relevant data
                    provider_response = data.get("provider_response", {})
                    transaction_final_amount_in_kobo = provider_response.get("transaction_final_amount")
                    reference = provider_response.get("reference")       
                    meta = provider_response.get("meta")  

                    transaction_final_amount = Onepipe.amount_in_naira(transaction_final_amount_in_kobo)

                    Payment.objects.create(
                        email = email,
                        company=company,
                        reference = reference,
                        request_ref = request_ref,
                        transaction_ref = transaction_ref,
                        transaction_final_amount_in_kobo = transaction_final_amount_in_kobo,
                        transaction_final_amount = transaction_final_amount,
                        phone_number = phone_number,
                        first_name = first_name,
                        last_name = last_name,
                        mock_mode = mock_mode,
                        bank_code = bank_cbn_code,
                        account_number = account_number,
                        # status = status,
                        meta = meta,
                        amount = amount,
                        amount_in_kobo = amount_in_kobo,
                    )
            return response_data
        else:
            response_data = {
                "status": "Failed", 
                "message": "Service is temporarily unavailable",
                "data": {
                    "provider_response_code": "400"
                }
            }
            return response_data
        
    @classmethod
    def query_reference(cls, validated_data, company):
        from one_pipe.models import PayWithAccount

        request_type = validated_data.get("request_type")
        transaction_ref = validated_data.get("transaction_ref")

        constant_variable = ConstantTable.get_constant_variable()
        if not constant_variable:
            mock_mode = "Inspect"
            on_off = False
        else:
            mock_mode = constant_variable.mock_mode
            on_off = constant_variable.on_off

        url = f"{cls.url}/v2/transact/query"
        ONEPIPE_API_KEY = cls.ONEPIPE_API_KEY
        request_ref = Onepipe.generate_request_ref()
        signature = Onepipe.signature(request_ref=request_ref)
        
        if on_off is True:
        
            headers = {
                "Authorization": f"Bearer {ONEPIPE_API_KEY}",
                "Signature": f"{signature}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "request_ref": f"{request_ref}",
                "request_type": f"{request_type}",
                "transaction":{
                    "transaction_ref": f"{transaction_ref}" 
                }
            }

            pay_with_account = PayWithAccount.objects.create(
                request_type="QUERY_TRANSACTION",
                company=company,
                mock_mode=mock_mode,
                request_ref=request_ref,
                # transaction_ref=transaction_ref,
                request=payload,
            )
            response = requests.request("POST", url, headers=headers, json=payload)
            response_data = response.json()

            pay_with_account.response = response_data
            pay_with_account.save()
            
            return response_data
        else:
            response_data = {
                "status": "Failed", 
                "message": "Service is temporarily unavailable",
                "data": {
                    "provider_response_code": "400"
                }
            }
            return response_data