from datetime import timedelta

from django.core.management.base import (
    BaseCommand,
    CommandError,
    CommandParser,
)
from django.db import transaction

from accounts import models
from helpers import enums
from helpers.reusable import session_maker
from user_profiles.models import Company


class Command(BaseCommand):
    help = "MANUAL FUNDING."

    @transaction.atomic()
    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("credit_account")
        parser.add_argument("amount")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        credit_account = kwargs["credit_account"]
        amount = float(kwargs["amount"])
        session = session_maker()

        account_details = models.AccountDetail.objects.filter(
            account_number=credit_account
        ).last()
        if account_details is None:
            raise CommandError("INVALID CREDIT ACCOUNT NUMBER PROVIDED!")

        credit_transaction = models.TransactionDetail.register_transaction(
            company=account_details.company,
            sub_company=account_details.sub_company,
            beneficiary_account_number=credit_account,
            beneficiary_account_name=account_details.account_name,
            amount=amount,
            fee=0.0,
            amount_payable=amount - 0.0,
            transaction_type=enums.TransactionType.CREDIT,
            transaction_status=enums.TransactionStatus.SUCCESSFUL,
            narration="wallet manual funding",
            service_provider=enums.ServiceProvider.CASH_CONNECT,
            mode=enums.APIMode.LIVE,
            session_id=session,
            bank_code="******",
            bank_name="CashConnect",
            source_account="**********",
            source_name="CashConnect",
            is_verified=True,
        )
        if not isinstance(credit_transaction, bool):
            wallet_transaction = models.AccountDetail.fund_account(
                company=account_details.company,
                account_type=account_details.account_type,
                amount=amount,
                charges=0.0,
                sub_company=account_details.sub_company,
                provider=account_details.provider,
            )
            if wallet_transaction.get("status"):
                credit_transaction.balance_before = wallet_transaction.get(
                    "previous_balance"
                )
                credit_transaction.balance_after = wallet_transaction.get(
                    "account_balance"
                )
                credit_transaction.save()
        self.stdout.write(
            self.style.SUCCESS(
                "SYSTEM HAS COMPLETED A MANUAL FUNDING"
            )
        )
