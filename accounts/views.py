from datetime import datetime, timedelta

from django.db.models import Q
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from accounts import models, serializers
from accounts.utils import process_wema_account_statement
from helpers import enums
from helpers.custom_permissions import (
    CompanyCanSendMoney,
    IsCompanyVerified,
    IsIPWhitelisted,
)
from helpers.custom_responses import Response


# Create your view(s) here.
class AccountDetailAPIView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.AccountDetailSerializer

    def get(self, request, *args, **kwargs):
        provider = request.query_params.get("provider")
        if not provider:
            provider = enums.ServiceProvider.WEMA_BANK
        account_detail = models.AccountDetail.objects.filter(
            company=request.user,
            provider=provider,
        ).last()
        if account_detail is not None:
            serializer = self.serializer_class(instance=account_detail)
            data = {
                "message": "successfully fetched company account details.",
                "acount_details": serializer.data,
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)
        else:
            data = {
                "message": "company has no account details yet, contact support.",
                "account_details": {},
            }
            return Response(
                data=data, status_code=404, status=status.HTTP_404_NOT_FOUND
            )


class TransferMoneyAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        IsCompanyVerified,
        IsIPWhitelisted,
        CompanyCanSendMoney,
    ]
    serializer_class = serializers.TransferMoneySerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        if serializer.validated_data.get("mode") == enums.APIMode.TEST:
            data = {
                "status": True,
                "message": "success",
                "transaction": request.data,
                "account_balance": 0.0,
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)
        else:
            transfer_request = models.TransferMoneyRequest.register_transfer_request(
                company=request.user,
                **serializer.validated_data,
            )
            if not transfer_request.get("status"):
                data = {
                    "status": transfer_request.get("status"),
                    "message": transfer_request.get("message"),
                    "transaction": transfer_request.get("transaction"),
                    "account_balance": transfer_request.get("account_balance"),
                }
                return Response(data=data, status_code=402, status=status.HTTP_402_PAYMENT_REQUIRED)
            else:
                serializer = self.serializer_class(
                    instance=transfer_request.get("transaction")
                )
                data = {
                    "status": transfer_request.get("status"),
                    "message": transfer_request.get("message"),
                    "transaction": serializer.data,
                    "account_balance": transfer_request.get("account_balance"),
                }
                return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class VerifyTransferAPIView(APIView):
    permission_classes = [IsAuthenticated, IsCompanyVerified]

    def get(self, request, *args, **kwargs):
        search = request.GET.get("search")
        if search is None or search == "":
            return Response(
                errors={"message": "provide a search parameter."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        transaction = models.TransactionDetail.objects.filter(
            company=request.user
        ).filter(
            Q(company_reference=search) | Q(
                reference=search) | Q(session_id=search)
        )
        serializer = serializers.VerifyTransferSerializer(
            instance=transaction, many=True
        )
        return Response(
            data={"transactions": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class AccountTransactionsAPIView(APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        account_number = request.query_params.get("account_number")
        if not account_number.startswith("842"):
            return Response(
                errors={"message": "provide a valid account number."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        transactions = models.TransactionDetail.objects.filter(
            beneficiary_account_number=account_number
        )
        serializer = serializers.TransactionDetailSerializer(
            instance=transactions, many=True
        )
        return Response(
            data={"transactions": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class ProcessWemaAccountStatementAPIView(APIView):
    """
    API endpoint for processing Wema Account Statement to query TransactionDetail records
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.ExcelFileUploadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                errors=serializer.errors,
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        uploaded_file = serializer.validated_data['file']

        result = process_wema_account_statement(uploaded_file)
        if not result['success']:
            return Response(
                errors={"message": result['error']},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            data={
                "message": "Excel file processed successfully",
                "total_count": result['total_count'],
                "found_count": result['found_count'],
                "missing_count": result['missing_count'],
                "download_url": result['download_url']
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )
