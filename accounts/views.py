from datetime import datetime, timedelta

from django.db.models import Q
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from accounts import models, serializers
from helpers import enums
from helpers.custom_permissions import (
    CompanyCanSendMoney,
    IsCompanyVerified,
    IsIPWhitelisted,
)
from helpers.custom_responses import Response


# Create your view(s) here.
class AccountDetailAPIView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.AccountDetailSerializer

    def get(self, request, *args, **kwargs):
        provider = request.query_params.get("provider")
        if not provider:
            provider = enums.ServiceProvider.WEMA_BANK
        account_detail = models.AccountDetail.objects.filter(
            company=request.user,
            provider=provider,
        ).last()
        if account_detail is not None:
            serializer = self.serializer_class(instance=account_detail)
            data = {
                "message": "successfully fetched company account details.",
                "acount_details": serializer.data,
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)
        else:
            data = {
                "message": "company has no account details yet, contact support.",
                "account_details": {},
            }
            return Response(
                data=data, status_code=404, status=status.HTTP_404_NOT_FOUND
            )


class TransferMoneyAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        IsCompanyVerified,
        IsIPWhitelisted,
        CompanyCanSendMoney,
    ]
    serializer_class = serializers.TransferMoneySerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        if serializer.validated_data.get("mode") == enums.APIMode.TEST:
            data = {
                "status": True,
                "message": "success",
                "transaction": request.data,
                "account_balance": 0.0,
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)
        else:
            transfer_request = models.TransferMoneyRequest.register_transfer_request(
                company=request.user,
                **serializer.validated_data,
            )
            if not transfer_request.get("status"):
                data = {
                    "status": transfer_request.get("status"),
                    "message": transfer_request.get("message"),
                    "transaction": transfer_request.get("transaction"),
                    "account_balance": transfer_request.get("account_balance"),
                }
                return Response(data=data, status_code=402, status=status.HTTP_402_PAYMENT_REQUIRED)
            else:
                serializer = self.serializer_class(
                    instance=transfer_request.get("transaction")
                )
                data = {
                    "status": transfer_request.get("status"),
                    "message": transfer_request.get("message"),
                    "transaction": serializer.data,
                    "account_balance": transfer_request.get("account_balance"),
                }
                return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class VerifyTransferAPIView(APIView):
    permission_classes = [IsAuthenticated, IsCompanyVerified]

    def get(self, request, *args, **kwargs):
        search = request.GET.get("search")
        if search is None or search == "":
            return Response(
                errors={"message": "provide a search parameter."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        transaction = models.TransactionDetail.objects.filter(
            company=request.user
        ).filter(
            Q(company_reference=search) | Q(
                reference=search) | Q(session_id=search)
        )
        serializer = serializers.VerifyTransferSerializer(
            instance=transaction, many=True
        )
        return Response(
            data={"transactions": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class AccountTransactionsAPIView(APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        account_number = request.query_params.get("account_number")
        if not account_number.startswith("842"):
            return Response(
                errors={"message": "provide a valid account number."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        transactions = models.TransactionDetail.objects.filter(
            beneficiary_account_number=account_number
        )
        serializer = serializers.TransactionDetailSerializer(
            instance=transactions, many=True
        )
        return Response(
            data={"transactions": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )

##################################################################################################################
# class LocalCreateTransactions(APIView):

#     def post(self, request):
#         from config.settings import ENVIRONMENT
#         from user_profiles.models import Company as CompanyInLocal

#         if ENVIRONMENT != "development":
#             return Response(
#                 data={"message": "Endpoint only meant for development"},
#                 status_code=403,
#                 status=status.HTTP_403_FORBIDDEN,
#             )

#         company_name = request.data.get("company_name")
#         amount = request.data.get("amount")
#         transaction_type = request.data.get("transaction_type")
#         amount_paybale = request.data.get("amount_paybale")
#         transaction_status = request.data.get("transaction_status")
#         beneficiary_account_number = request.data.get("beneficiary_account_number")
#         beneficiary_account_name = request.data.get("beneficiary_account_name")
#         narration = request.data.get("narration")
#         service_provider = request.data.get("service_provider")
#         mode = request.data.get("mode")
#         fee = request.data.get("fee")

#         company = CompanyInLocal.objects.filter(name=company_name).first()
#         transaction = TransactionDetail.register_transaction(
#             company=company,
#             beneficiary_account_name=beneficiary_account_name,
#             beneficiary_account_number=beneficiary_account_number,
#             amount=amount,
#             fee=fee,
#             amount_payable=amount_paybale,
#             transaction_status=transaction_status,
#             transaction_type=transaction_type,
#             narration=narration,
#             service_provider=service_provider,
#             mode=mode
#         )

#         return Response(
#                 data={"message": "Transaction created successfully", "transaction_ref": transaction.reference},
#                 status_code=200,
#                 status=status.HTTP_200_OK,
#             )
##################################################################################################################
