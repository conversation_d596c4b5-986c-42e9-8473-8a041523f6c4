from django.urls import include, path

from accounts import views
from wema_bank.views import WemaCallbackAPIView


# Create your urls pattern(s) here.
account_urls = [
    path("details", views.AccountDetailAPIView.as_view()),
    path("transactions", views.AccountTransactionsAPIView.as_view()),
]

transfer_urls = [
    path("transfer_money/", views.TransferMoneyAPIView.as_view(), name="default_transfer_money"),
    path("verify_transfer", views.VerifyTransferAPIView.as_view()),

]

wema_urls = [
    path("user/wema/callback", WemaCallbackAPIView.as_view()),
]

# dashboard = [
#     path("transactions_details/", views.TransactionDetailsViewsAPI.as_view({'get': 'list'})),
#     # path("test_create_transaction", views.LocalCreateTransactions.as_view())
# ]


urlpatterns = [
    path("", include(account_urls)),
    path("", include(transfer_urls)),
    path("", include(wema_urls)),
    # path("", include(dashboard))
]
