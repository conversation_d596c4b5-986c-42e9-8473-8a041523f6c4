from datetime import datetime
import os
from typing import Dict, Any
import pandas as pd
from django.conf import settings

from accounts.models import (
    AccountDetail,
    TransactionDetail,
    TransferMoneyRequest,
)
from accounts.tasks import (
    inward_transfer_handler,
    outward_transfer_handler,
)
from helpers import enums


# Utility manager.
def wema_funds_transfer(request_id: str):
    transfer_request = TransferMoneyRequest.objects.filter(
        id=request_id,
        status=enums.TransactionStatus.PENDING,
    ).last()
    if transfer_request.mode == enums.APIMode.LIVE:
        charge_user = AccountDetail.charge_account(
            company=transfer_request.company,
            source_account=transfer_request.source_account,
            amount=transfer_request.amount,
        )
        if not charge_user.get("status"):
            transfer_request.invalid_request = True
            transfer_request.balance_before = float(
                charge_user.get("previous_balance")
            )
            transfer_request.balance_after = float(
                charge_user.get("account_balance")
            )
            transfer_request.status = enums.TransactionStatus.FAILED
            transfer_request.wallet_charge_response = str(charge_user)
            transfer_request.save()
            # company associated debit transaction.
            debit_transaction = TransactionDetail.register_transaction(
                company=transfer_request.company,
                beneficiary_account_number=transfer_request.account_number,
                beneficiary_account_name=transfer_request.account_name,
                amount=transfer_request.amount,
                fee=0.0,  # currently hard-coded.
                amount_payable=transfer_request.amount,
                transaction_type=enums.TransactionType.DEBIT,
                transaction_status=enums.TransactionStatus.FAILED,
                narration=transfer_request.narration,
                service_provider=enums.ServiceProvider.WEMA_BANK,
                mode=transfer_request.mode,
                transfer_mode=transfer_request.transfer_mode,
                bank_code=transfer_request.bank_code,
                source_account=transfer_request.source_account,
                company_reference=transfer_request.request_reference,
            )
            if not isinstance(debit_transaction, bool):
                debit_transaction.balance_before = float(
                    charge_user.get("previous_balance")
                )
                debit_transaction.balance_after = float(
                    charge_user.get("account_balance")
                )
                debit_transaction.wallet_charge_response = str(charge_user)
                debit_transaction.save()
                return {
                    "status": False,
                    "message": charge_user.get("message"),
                    "transaction": None,
                    "account_balance": None,
                }
            else:
                return {
                    "status": False,
                    "message": "duplicate transaction",
                    "transaction": None,
                    "account_balance": float(charge_user.get("account_balance")),
                }
        if charge_user.get("status"):
            transfer_request.balance_before = float(
                charge_user.get("previous_balance")
            )
            transfer_request.balance_after = float(
                charge_user.get("account_balance")
            )
            transfer_request.wallet_charge_response = str(charge_user)
            transfer_request.save()
            # company associated debit transaction.
            debit_transaction = TransactionDetail.register_transaction(
                company=transfer_request.company,
                beneficiary_account_number=transfer_request.account_number,
                beneficiary_account_name=transfer_request.account_name,
                amount=transfer_request.amount,
                fee=0.0,  # currently hard-coded.
                amount_payable=transfer_request.amount,
                transaction_type=enums.TransactionType.DEBIT,
                transaction_status=enums.TransactionStatus.PENDING,
                narration=transfer_request.narration,
                service_provider=enums.ServiceProvider.WEMA_BANK,
                mode=transfer_request.mode,
                transfer_mode=transfer_request.transfer_mode,
                bank_code=transfer_request.bank_code,
                source_account=transfer_request.source_account,
                company_reference=transfer_request.request_reference,
            )
            if not isinstance(debit_transaction, bool):
                debit_transaction.balance_before = float(
                    charge_user.get("previous_balance")
                )
                debit_transaction.balance_after = float(
                    charge_user.get("account_balance")
                )
                debit_transaction.wallet_charge_response = str(charge_user)
                debit_transaction.save()
                if transfer_request.transfer_mode == enums.TransferMode.INTERNAL:
                    inward_transfer_handler.apply_async(
                        queue="nip",
                        args=[
                            transfer_request.id,
                        ],
                    )
                    return {
                        "status": True,
                        "message": "request completed successfully.",
                        "transaction": transfer_request,
                        "account_balance": float(charge_user.get("account_balance")),
                    }
                else:
                    outward_transfer_handler.apply_async(
                        queue="nip",
                        args=[
                            transfer_request.id,
                        ],
                    )
                    return {
                        "status": True,
                        "message": "request completed successfully.",
                        "transaction": transfer_request,
                        "account_balance": float(charge_user.get("account_balance")),
                    }
            else:
                return {
                    "status": False,
                    "message": "duplicate transaction",
                    "transaction": None,
                    "account_balance": float(charge_user.get("account_balance")),
                }


def process_wema_account_statement(uploaded_file) -> Dict[str, Any]:
    """
    Process Excel file to query TransactionDetail records and add company name and transaction date.
    Args:
        uploaded_file: The uploaded Excel file
    Returns:
        Dict containing statistics and download link for processed file
    """
    try:
        df = pd.read_excel(uploaded_file)
        # Check if TranRmks column exists
        if 'TranRmks' not in df.columns:
            return {
                "success": False,
                "error": "TranRmks column not found in the Excel file"
            }
        # Initialize new columns
        df["Company Name"] = "NOT FOUND"
        df["Transaction Date"] = "NOT FOUND"

        # Get unique session_ids from TranRmks column (remove NaN values)
        session_ids = df["TranRmks"].dropna().unique()

        # Query TransactionDetail records for all session_ids at once
        transactions = TransactionDetail.objects.filter(
            session_id__in=session_ids
        ).select_related("company")

        # Create a mapping of session_id to transaction data
        transaction_map = {}
        for transaction in transactions:
            transaction_map[transaction.session_id] = {
                "company_name": transaction.company.name,
                "transaction_date": transaction.created_at.strftime("%Y-%m-%d %H:%M:%S")
            }

        found_count = 0
        total_count = len(df)

        for index, row in df.iterrows():
            session_id = row["TranRmks"]
            if pd.notna(session_id) and session_id in transaction_map:
                df.at[index, "Company Name"] = transaction_map[session_id]["company_name"]
                df.at[index, "Transaction Date"] = transaction_map[session_id]["transaction_date"]
                found_count += 1

        missing_count = total_count - found_count

        # Determine the month from the data (try to extract from transaction dates or use current month)
        report_month = "unknown"
        if found_count > 0:
            # Get the most common month from the transaction dates
            months = []
            for session_id in session_ids:
                if session_id in transaction_map:
                    transaction_date = transaction_map[session_id]["transaction_date"]
                    try:
                        date_obj = datetime.strptime(
                            transaction_date, "%Y-%m-%d %H:%M:%S")
                        # Full month name in lowercase
                        months.append(date_obj.strftime("%B").lower())
                    except:
                        continue

            if months:
                # Get the most frequent month
                report_month = max(set(months), key=months.count)
            else:
                report_month = datetime.now().strftime("%B").lower()
        else:
            report_month = datetime.now().strftime("%B").lower()

        # Generate file name using upload date
        upload_date = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"{upload_date}.xlsx"

        # Create the file path within MEDIA_ROOT to ensure proper serving
        media_file_path = os.path.join(
            settings.MEDIA_ROOT,
            "reports",
            "account_statement",
            f"{report_month}_report"
        )
        media_file_full_path = os.path.join(media_file_path, file_name)

        # Create directory if it doesn't exist
        os.makedirs(media_file_path, exist_ok=True)

        # Save the updated Excel file
        df.to_excel(media_file_full_path, index=False)

        # Generate download URL (relative to MEDIA_ROOT)
        relative_path = f"reports/account_statement/{report_month}_report/{file_name}"
        download_url = f"{settings.MEDIA_URL}{relative_path}"

        return {
            "success": True,
            "total_count": total_count,
            "found_count": found_count,
            "missing_count": missing_count,
            "download_url": download_url,
            "file_path": media_file_full_path,
            "report_category": "account_statement",
            "report_type": report_month,
            "file_name": file_name
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Error processing file: {str(e)}"
        }
