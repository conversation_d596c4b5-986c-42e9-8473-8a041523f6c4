from accounts.models import (
    AccountDetail,
    TransactionDetail,
    TransferMoneyRequest,
)
from accounts.tasks import (
    inward_transfer_handler,
    outward_transfer_handler,
)
from helpers import enums


# Utility manager.
def wema_funds_transfer(request_id: str):
    transfer_request = TransferMoneyRequest.objects.filter(
        id=request_id,
        status=enums.TransactionStatus.PENDING,
    ).last()
    if transfer_request.mode == enums.APIMode.LIVE:
        charge_user = AccountDetail.charge_account(
            company=transfer_request.company,
            source_account=transfer_request.source_account,
            amount=transfer_request.amount,
        )
        if not charge_user.get("status"):
            transfer_request.invalid_request = True
            transfer_request.balance_before = float(
                charge_user.get("previous_balance")
            )
            transfer_request.balance_after = float(
                charge_user.get("account_balance")
            )
            transfer_request.status = enums.TransactionStatus.FAILED
            transfer_request.wallet_charge_response = str(charge_user)
            transfer_request.save()
            # company associated debit transaction.
            debit_transaction = TransactionDetail.register_transaction(
                company=transfer_request.company,
                beneficiary_account_number=transfer_request.account_number,
                beneficiary_account_name=transfer_request.account_name,
                amount=transfer_request.amount,
                fee=0.0,  # currently hard-coded.
                amount_payable=transfer_request.amount,
                transaction_type=enums.TransactionType.DEBIT,
                transaction_status=enums.TransactionStatus.FAILED,
                narration=transfer_request.narration,
                service_provider=enums.ServiceProvider.WEMA_BANK,
                mode=transfer_request.mode,
                transfer_mode=transfer_request.transfer_mode,
                bank_code=transfer_request.bank_code,
                source_account=transfer_request.source_account,
                company_reference=transfer_request.request_reference,
            )
            if not isinstance(debit_transaction, bool):
                debit_transaction.balance_before = float(
                    charge_user.get("previous_balance")
                )
                debit_transaction.balance_after = float(
                    charge_user.get("account_balance")
                )
                debit_transaction.wallet_charge_response = str(charge_user)
                debit_transaction.save()
                return {
                    "status": False,
                    "message": charge_user.get("message"),
                    "transaction": None,
                    "account_balance": None,
                }
            else:
                return {
                    "status": False,
                    "message": "duplicate transaction",
                    "transaction": None,
                    "account_balance": float(charge_user.get("account_balance")),
                }
        if charge_user.get("status"):
            transfer_request.balance_before = float(
                charge_user.get("previous_balance")
            )
            transfer_request.balance_after = float(
                charge_user.get("account_balance")
            )
            transfer_request.wallet_charge_response = str(charge_user)
            transfer_request.save()
            # company associated debit transaction.
            debit_transaction = TransactionDetail.register_transaction(
                company=transfer_request.company,
                beneficiary_account_number=transfer_request.account_number,
                beneficiary_account_name=transfer_request.account_name,
                amount=transfer_request.amount,
                fee=0.0,  # currently hard-coded.
                amount_payable=transfer_request.amount,
                transaction_type=enums.TransactionType.DEBIT,
                transaction_status=enums.TransactionStatus.PENDING,
                narration=transfer_request.narration,
                service_provider=enums.ServiceProvider.WEMA_BANK,
                mode=transfer_request.mode,
                transfer_mode=transfer_request.transfer_mode,
                bank_code=transfer_request.bank_code,
                source_account=transfer_request.source_account,
                company_reference=transfer_request.request_reference,
            )
            if not isinstance(debit_transaction, bool):
                debit_transaction.balance_before = float(
                    charge_user.get("previous_balance")
                )
                debit_transaction.balance_after = float(
                    charge_user.get("account_balance")
                )
                debit_transaction.wallet_charge_response = str(charge_user)
                debit_transaction.save()
                if transfer_request.transfer_mode == enums.TransferMode.INTERNAL:
                    inward_transfer_handler.apply_async(
                        queue="nip",
                        args=[
                            transfer_request.id,
                        ],
                    )
                    return {
                        "status": True,
                        "message": "request completed successfully.",
                        "transaction": transfer_request,
                        "account_balance": float(charge_user.get("account_balance")),
                    }
                else:
                    outward_transfer_handler.apply_async(
                        queue="nip",
                        args=[
                            transfer_request.id,
                        ],
                    )
                    return {
                        "status": True,
                        "message": "request completed successfully.",
                        "transaction": transfer_request,
                        "account_balance": float(charge_user.get("account_balance")),
                    }
            else:
                return {
                    "status": False,
                    "message": "duplicate transaction",
                    "transaction": None,
                    "account_balance": float(charge_user.get("account_balance")),
                }
