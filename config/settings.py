from datetime import timedelta
import logging
import os
from pathlib import Path

from django.core.management.utils import get_random_secret_key
from decouple import Csv, config
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration


LOGGER = logging.getLogger("__name__")


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY", default=get_random_secret_key(), cast=str)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config("DEBUG", default=False, cast=bool)

ALLOWED_HOSTS = config("ALLOWED_HOSTS", cast=Csv())

ENVIRONMENT = config("ENVIRONMENT")

# Application definition
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Local
    "accounts",
    "core",
    "user_profiles",
    "wema_bank",
    "zenith_bank",
    "fidelity",
    "vfd_bank",
    "cash_connect",
    'dashboard',
    "one_pipe",

    # Third party
    "django_celery_beat",
    "django_celery_results",
    "drf_yasg",
    "import_export",
    "rest_framework",
    "rest_framework.authtoken",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

CORS_ALLOW_ALL_ORIGINS = True

WSGI_APPLICATION = "config.wsgi.application"


# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": config("DB_NAME"),
        "USER": config("DB_USER"),
        "PASSWORD": config("DB_PASS"),
        "HOST": config("DB_HOST"),
        "PORT": config("DB_PORT"),
    }
}

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Africa/Lagos"

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/
STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")

MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

# Extra lookup directories for collectstatic to find static files
STATICFILES_DIRS = []

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

AUTH_USER_MODEL = "user_profiles.Company"

# Rest framework config.
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication"
    ],
    "EXCEPTION_HANDLER": "helpers.custom_exceptions.custom_exception_handler",
    "COERCE_DECIMAL_TO_STRING": False,

    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 25
}

# API key config.
API_KEY_DELIMITER = config("API_KEY_DELIMITER")
API_KEY_PREFIX = "Api_key "

APPEND_SLASH = config("APPEND_SLASH")

CALLBACK_BEARER = config("CALLBACK_BEARER")

redis_host = "127.0.0.1"

if ENVIRONMENT == "container":
    redis_host = "redis_corebanking"

# Celery configuration.
CELERY_BROKER_URL = f"redis://{redis_host}:6379/5"
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_TIMEZONE = TIME_ZONE
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_RESULT_BACKEND = "django-db"

CORE_BANKING_EMAIL = config("CORE_BANKING_EMAIL")

MAILGUN_API_KEY = config("MAILGUN_API_KEY")
MAILGUN_URL = config("MAILGUN_URL")
MOTHER_BVN = config("MOTHER_BVN")

OTP_SECRET = config("OTP_SECRET")

SECRET_PASSWORD = config("SECRET_PASSWORD")
SHARED_BEARER = config("SHARED_BEARER")

# Rest Framework Simple JWT.
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=1),
    "ALGORITHM": "HS512",
    "SIGNING_KEY": SECRET_KEY,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
}
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{redis_host}:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}
DEFAULT_CACHE_TIMEOUT = 20
USSD_BEARER = config("USSD_BEARER")

# Wema Bank config.
WEMA_BANK_AES_IV = config("WEMA_BANK_AES_IV")
WEMA_BANK_AES_KEY = config("WEMA_BANK_AES_KEY")
WEMA_BASE_URL = config("WEMA_BASE_URL")
WEMA_BEARER = config("WEMA_BEARER")
WEMA_PASSWORD = config("WEMA_PASSWORD")
WEMA_SOURCE_ACCOUNT = config("WEMA_SOURCE_ACCOUNT")
WEMA_USER_ID = config("WEMA_USER_ID")
WEMA_USERNAME = config("WEMA_USERNAME")
WEMA_VENDOR_ID = config("WEMA_VENDOR_ID")

# Zenith Bank config.
ZENITH_BASE_URL = config("ZENITH_BASE_URL")
ZENITH_BASE_URL_LIVE = config("ZENITH_BASE_URL_LIVE")
ZENITH_PROTECTOR = config("ZENITH_PROTECTOR")
ZENITH_PROTECTOR_LIVE = config("ZENITH_PROTECTOR_LIVE")
ZENITH_SOURCE_ACCOUNT = config("ZENITH_SOURCE_ACCOUNT")
ZENITH_USER = config("ZENITH_USER")
ZENITH_USER_LIVE = config("ZENITH_USER_LIVE")


# Fidelity Bank Config
FIDELITY_BASE_URL = config("FIDELITY_BASE_URL")
FIDELITY_API_KEY = config("FIDELITY_API_KEY")
FIDELITY_SECRET_KEY = config("FIDELITY_SECRET_KEY")
FIDELITY_SOURCE_ACCOUNT_NUMBER = config("FIDELITY_SOURCE_ACCOUNT_NUMBER")
FIDELITY_BANK_CODE = config("FIDELITY_BANK_CODE")

# Paystack Config
PAYSTACK_BASE_URL = config("PAYSTACK_BASE_URL")
PAYSTACK_SECRET_KEY = config("PAYSTACK_SECRET_KEY")

# VFD Bank Config
VFD_ACCESS_TOKEN = config("VFD_ACCESS_TOKEN")

if not DEBUG and ENVIRONMENT == "production":
    sentry_sdk.init(
        dsn=config("SENTRY_DSN"),
        integrations=[DjangoIntegration()],
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production.
        traces_sample_rate=0.001,
        # To set a uniform sample rate
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production,
        profiles_sample_rate=1.0,
    )

# Onepipe PaywithAccount
ONEPIPE_SECRET_KEY = config("ONEPIPE_SECRET_KEY")
ONEPIPE_API_KEY = config("ONEPIPE_API_KEY")
