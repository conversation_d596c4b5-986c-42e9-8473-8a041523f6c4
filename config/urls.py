from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework.permissions import AllowAny


# Swagger-UI.
schema_view = get_schema_view(
    openapi.Info(
        title="Liberty Technologies - Core Banking Service",
        default_version="v1",
        description="API Master",
        terms_of_service="https://www.libertyassured.com/whisper-terms-conditions-1-1",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="MIT License"),
    ),
    public=True,
    permission_classes=[
        AllowAny,
    ],
)

admin.site.site_header = "Core Banking Service"


urlpatterns = [
    path(
        "docs/",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    path("admin/", admin.site.urls),
    path("api/v1/core/", include("core.urls")),
    path("accounts/", include("accounts.urls")),
    path("api/v1/companies/", include("user_profiles.urls")),
    path("api/v1/wema/", include("wema_bank.urls")),
    path("api/v1/zenith/", include("zenith_bank.urls")),
    path('api/v1/fidelity/', include('fidelity.urls')),
    path('api/v1/cash-connect/', include('cash_connect.urls')),
    path('dashboard/', include('dashboard.urls')),
    path('api/v1/vfd/', include('vfd_bank.urls')),
    path('api/v1/paystack/', include('paystack.urls')),
    path('api/v1/one_pipe/', include('one_pipe.urls')),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
